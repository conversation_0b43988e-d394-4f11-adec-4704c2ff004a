#!/usr/bin/env python3
"""
Debug script to test symbol finding in the Svelte test repository.
"""

import os
import sys
sys.path.append('/Users/<USER>/MCP/serena/src')

from solidlsp.ls import SolidLanguageServer
from solidlsp.ls_config import Language, LanguageServerConfig
from solidlsp.ls_logger import LanguageServerLogger
from serena.symbol import LanguageServerSymbolRetriever
import logging

def test_symbol_finding():
    test_repo_path = "/Users/<USER>/MCP/serena/test/resources/repos/svelte/test_repo"
    print(f"Testing symbol finding in: {test_repo_path}")
    
    # Create logger
    logger = LanguageServerLogger("debug", logging.DEBUG)
    
    # Create config
    config = LanguageServerConfig(
        code_language=Language.SVELTE,
        trace_lsp_communication=False
    )
    
    # Create language server
    ls = SolidLanguageServer.create(
        config=config,
        logger=logger,
        repository_root_path=test_repo_path
    )
    
    try:
        ls.start()
        print("Language server started successfully")
        
        # Create symbol retriever
        symbol_retriever = LanguageServerSymbolRetriever(ls)
        
        print("\n=== Testing find_by_name for formatName in utils.ts ===")
        symbols = symbol_retriever.find_by_name(
            name_path="formatName", 
            within_relative_path="lib/utils.ts"
        )
        print(f"Found {len(symbols)} symbols with name 'formatName' in lib/utils.ts")
        for sym in symbols:
            print(f"  Symbol: {sym.get_name_path()} at {sym.location.to_dict()}")
        
        print("\n=== Testing find_by_name for formatName globally ===")
        symbols_global = symbol_retriever.find_by_name(
            name_path="formatName"
        )
        print(f"Found {len(symbols_global)} symbols with name 'formatName' globally")
        for sym in symbols_global:
            print(f"  Symbol: {sym.get_name_path()} at {sym.location.to_dict()}")
        
        print("\n=== Testing request_full_symbol_tree for utils.ts ===")
        symbol_tree = ls.request_full_symbol_tree(within_relative_path="lib/utils.ts")
        print(f"Found {len(symbol_tree)} symbol trees in lib/utils.ts")
        for i, tree in enumerate(symbol_tree):
            print(f"  Tree {i}: {tree.get('name', 'unnamed')} (kind: {tree.get('kind', 'unknown')})")
        
        print("\n=== Testing document symbols for utils.ts ===")
        doc_symbols, roots = ls.request_document_symbols("lib/utils.ts")
        print(f"Found {len(doc_symbols)} document symbols in lib/utils.ts")
        for sym in doc_symbols:
            print(f"  Symbol: {sym.get('name', 'unnamed')} (kind: {sym.get('kind', 'unknown')})")
            
    finally:
        ls.stop()
        print("\nLanguage server stopped")

if __name__ == "__main__":
    test_symbol_finding()