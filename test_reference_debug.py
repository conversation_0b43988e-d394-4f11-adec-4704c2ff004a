#!/usr/bin/env python3
"""
Debug script to test reference finding in the Svelte test repository.
"""

import os
import sys
import logging
from pathlib import Path

# Add the src directory to the Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from solidlsp.language_servers.typescript_language_server import TypeScriptLanguageServer
from solidlsp.ls_config import LanguageServerConfig
from solidlsp.ls_logger import LanguageServerLogger

def test_typescript_references():
    """Test TypeScript reference finding directly."""
    
    # Setup
    test_repo_path = "/Users/<USER>/MCP/serena/test/resources/repos/svelte/test_repo"
    
    # Create logger
    logger = LanguageServerLogger(log_level=logging.DEBUG)
    
    # Create config
    from solidlsp.ls_config import Language
    config = LanguageServerConfig(code_language=Language.TYPESCRIPT)
    
    print(f"Testing TypeScript reference finding in: {test_repo_path}")
    
    # Create TypeScript language server
    ts_server = TypeScriptLanguageServer(config, logger, test_repo_path)
    
    try:
        with ts_server.start_server():
            print("TypeScript server started successfully")
            
            # Test 1: Find formatName function
            print("\n=== Test 1: Finding formatName function ===")
            try:
                symbols = ts_server.request_full_symbol_tree("lib/utils.ts", include_body=False)
                print(f"Found {len(symbols)} symbols in utils.ts")
                for symbol in symbols:
                    if symbol.get("name") == "formatName":
                        print(f"Found formatName symbol: {symbol}")
                        location = symbol.get("location", {})
                        range_info = location.get("range", {})
                        start = range_info.get("start", {})
                        line = start.get("line", 0)
                        column = start.get("character", 0)
                        
                        print(f"Testing references at line {line}, column {column}")
                        
                        # Test reference finding
                        refs = ts_server.request_references("lib/utils.ts", line, column)
                        print(f"Found {len(refs)} references to formatName")
                        for ref in refs:
                            print(f"  Reference: {ref}")
                        break
                else:
                    print("formatName symbol not found")
            except Exception as e:
                print(f"Error testing formatName: {e}")
            
            # Test 2: Find User interface
            print("\n=== Test 2: Finding User interface ===")
            try:
                symbols = ts_server.request_full_symbol_tree("lib/store.ts", include_body=False)
                print(f"Found {len(symbols)} symbols in store.ts")
                for symbol in symbols:
                    if symbol.get("name") == "User":
                        print(f"Found User symbol: {symbol}")
                        location = symbol.get("location", {})
                        range_info = location.get("range", {})
                        start = range_info.get("start", {})
                        line = start.get("line", 0)
                        column = start.get("character", 0)
                        
                        print(f"Testing references at line {line}, column {column}")
                        
                        # Test reference finding
                        refs = ts_server.request_references("lib/store.ts", line, column)
                        print(f"Found {len(refs)} references to User")
                        for ref in refs:
                            print(f"  Reference: {ref}")
                        break
                else:
                    print("User symbol not found")
            except Exception as e:
                print(f"Error testing User: {e}")
                
    except Exception as e:
        print(f"Error starting TypeScript server: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_typescript_references()