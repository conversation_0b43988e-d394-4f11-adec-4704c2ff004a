#!/usr/bin/env python3
"""
Test VTS reference finding with a simple TypeScript-only scenario.
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# Add the src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from solidlsp.ls import SolidLanguageServer
from solidlsp.ls_config import Language, LanguageServerConfig
from solidlsp.ls_logger import LanguageServerLogger

def test_simple_ts_references():
    """Test VTS with a simple TypeScript project."""
    
    # Create a temporary directory for our test
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"📁 Created temp directory: {temp_dir}")
        
        # Create simple TypeScript files
        utils_content = '''
export function greet(name: string): string {
    return `Hello, ${name}!`;
}

export const DEFAULT_NAME = "World";
'''
        
        main_content = '''
import { greet, DEFAULT_NAME } from './utils';

console.log(greet(DEFAULT_NAME));
console.log(greet("TypeScript"));
'''
        
        tsconfig_content = '''
{
  "compilerOptions": {
    "target": "ES2020",
    "module": "ESNext",
    "moduleResolution": "node",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true
  },
  "include": ["*.ts"]
}
'''
        
        # Write the files
        utils_path = os.path.join(temp_dir, "utils.ts")
        main_path = os.path.join(temp_dir, "main.ts")
        tsconfig_path = os.path.join(temp_dir, "tsconfig.json")
        
        with open(utils_path, 'w') as f:
            f.write(utils_content)
        with open(main_path, 'w') as f:
            f.write(main_content)
        with open(tsconfig_path, 'w') as f:
            f.write(tsconfig_content)
            
        print("📝 Created test files:")
        print(f"   - utils.ts")
        print(f"   - main.ts")
        print(f"   - tsconfig.json")
        
        try:
            # Initialize VTS language server
            logger = LanguageServerLogger(log_level="INFO")
            config = LanguageServerConfig(code_language=Language.TYPESCRIPT_VTS)
            vts_server = SolidLanguageServer.create(config, logger, temp_dir)
            
            print("\n🚀 Starting VTS server...")
            vts_server.start()
            print("✅ VTS server started successfully")
            
            # Give the server time to initialize
            import time
            print("⏳ Waiting for server to initialize...")
            time.sleep(3)
            
            # Test finding document symbols in utils.ts
            print("\n📍 Finding symbols in utils.ts...")
            symbols = vts_server.request_document_symbols("utils.ts")
            print(f"Document symbols: {len(symbols[0]) if symbols else 0} symbols found")
            
            # Find the greet function
            greet_symbol = None
            for symbol in symbols[0] if symbols else []:
                if symbol.get("name") == "greet":
                    greet_symbol = symbol
                    break
                    
            if greet_symbol:
                print(f"✅ Found greet function: {greet_symbol['name']}")
                
                # Test finding references
                sel_start = greet_symbol["selectionRange"]["start"]
                print(f"\n🔍 Finding references to greet at line {sel_start['line']}, char {sel_start['character']}...")
                references = vts_server.request_references("utils.ts", sel_start["line"], sel_start["character"])
                
                if references:
                    print(f"✅ Found {len(references)} references:")
                    for ref in references:
                        file_path = ref['uri'].replace(f"file://{temp_dir}/", "")
                        line = ref['range']['start']['line'] + 1
                        print(f"   - {file_path}:{line}")
                else:
                    print("❌ No references found")
            else:
                print("❌ greet function not found in symbols")
                
        except Exception as e:
            print(f"❌ Error: {e}")
            import traceback
            traceback.print_exc()
        finally:
            try:
                print("\n🛑 Stopping VTS server...")
                vts_server.stop()
                print("✅ VTS server stopped")
            except:
                pass

if __name__ == "__main__":
    test_simple_ts_references()