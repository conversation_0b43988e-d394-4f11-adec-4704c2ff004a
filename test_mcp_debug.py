#!/usr/bin/env python3
"""
Debug script to test MCP server find_referencing_symbols functionality.
"""

import sys
from pathlib import Path

# Add the src directory to the Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from serena.mcp import SerenaMCPFactorySingleProcess
from serena.agent import SerenaAgent
from serena.config.context_mode import SerenaAgentContext
from serena.config.serena_config import SerenaConfig

def test_mcp_find_referencing_symbols():
    """Test MCP server find_referencing_symbols functionality."""
    
    # Set up the test repository path
    test_repo_path = "/Users/<USER>/MCP/serena/test/resources/repos/svelte/test_repo"
    
    print(f"Testing MCP find_referencing_symbols in: {test_repo_path}")
    
    try:
        # Create MCP factory with the test project
        factory = SerenaMCPFactorySingleProcess(project=test_repo_path)
        
        # Create config and agent manually to test
        config = SerenaConfig.from_config_file()
        context = SerenaAgentContext.load("desktop-app")
        
        # Create agent
        agent = SerenaAgent(
            project=test_repo_path,
            serena_config=config,
            context=context,
            modes=[],
            memory_log_handler=None
        )
        
        print("Agent created successfully")
        
        # Check if project is properly loaded
        active_project = agent.get_active_project()
        print(f"Agent active project: {active_project}")
        if active_project:
            print(f"Project root: {active_project.project_root}")
        
        # Wait for language server to be ready
        import time
        print("Waiting for language server to initialize...")
        max_wait = 30  # 30 seconds timeout
        wait_time = 0
        while not agent.is_language_server_running() and wait_time < max_wait:
            time.sleep(1)
            wait_time += 1
            if wait_time % 5 == 0:
                print(f"Still waiting... ({wait_time}s)")
        
        if agent.is_language_server_running():
            print("Language server is running!")
        else:
            print("Language server failed to start within timeout")
            return
        
        # Get the FindReferencingSymbolsTool
        from serena.tools.symbol_tools import FindReferencingSymbolsTool
        
        # Find the tool in the agent's tools
        ref_tool = None
        for tool in agent.get_exposed_tool_instances():
            if isinstance(tool, FindReferencingSymbolsTool):
                ref_tool = tool
                break
        
        if ref_tool is None:
            print("FindReferencingSymbolsTool not found in agent tools")
            return
        
        print("Found FindReferencingSymbolsTool")
        
        # Test the tool directly
        print("\n=== Testing FindReferencingSymbolsTool.apply ====")
        try:
            result = ref_tool.apply(
                name_path="formatName",
                relative_path="lib/utils.ts"
            )
            print(f"Tool result: {result}")
        except Exception as e:
            print(f"Tool apply failed: {e}")
            import traceback
            traceback.print_exc()
        
        # Test the underlying symbol retriever
        print("\n=== Testing underlying symbol retriever ====")
        try:
            symbol_retriever = ref_tool.create_language_server_symbol_retriever()
            print(f"Symbol retriever created: {type(symbol_retriever)}")
            
            # Test find_by_name first
            symbols = symbol_retriever.find_by_name("formatName", within_relative_path="lib/utils.ts")
            print(f"Found {len(symbols)} symbols named formatName")
            
            if symbols:
                # Test find_referencing_symbols
                refs = symbol_retriever.find_referencing_symbols("formatName", "lib/utils.ts")
                print(f"Found {len(refs)} references to formatName")
                for ref in refs:
                    print(f"  - {ref.symbol.name} in {ref.symbol.location.relative_path}:{ref.line}:{ref.character}")
            else:
                print("No formatName symbols found, cannot test references")
                
        except Exception as e:
            print(f"Symbol retriever test failed: {e}")
            import traceback
            traceback.print_exc()
            
    except Exception as e:
        print(f"Error setting up MCP test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_mcp_find_referencing_symbols()