import os
import sys
sys.path.insert(0, 'src')

from solidlsp.ls_config import Language, LanguageServerConfig
from solidlsp.ls import SolidLanguageServer
from solidlsp.ls_logger import LanguageServerLogger
import logging

# Setup
repo_path = os.path.abspath("test/resources/repos/svelte/test_repo")
config = LanguageServerConfig(Language.SVELTE)
logger = LanguageServerLogger(log_level=logging.INFO)

print(f"Creating Svelte language server for {repo_path}")
ls = SolidLanguageServer.create(config, logger, repo_path)

try:
    ls.start()
    print("Language server started\n")
    
    # Test 1: Find the formatName function
    print("=== Test 1: Find formatName function ===")
    symbols = ls.request_document_symbols(os.path.join("lib", "utils.ts"))
    print(f"Found {len(symbols[0])} symbols in utils.ts")
    for sym in symbols[0]:
        if sym.get('name') == 'formatName':
            print(f"Found formatName at line {sym['location']['range']['start']['line'] + 1}")
    
    # Test 2: Find references to formatName using the LS directly
    print("\n=== Test 2: Find references to formatName (line 2, col 17) ===")
    refs = ls.request_references(os.path.join("lib", "utils.ts"), 2, 17)
    print(f"Language server found {len(refs)} references:")
    for ref in refs:
        print(f"  - {ref['uri']}: Line {ref['range']['start']['line'] + 1}")
    
    # Test 3: Same but at the beginning of the function name
    print("\n=== Test 3: Find references to formatName (line 2, col 16) ===")
    refs = ls.request_references(os.path.join("lib", "utils.ts"), 2, 16)
    print(f"Language server found {len(refs)} references:")
    for ref in refs:
        print(f"  - {ref['uri']}: Line {ref['range']['start']['line'] + 1}")
        
    # Test 4: Check at different positions
    for col in range(16, 26):
        refs = ls.request_references(os.path.join("lib", "utils.ts"), 2, col)
        if refs:
            print(f"\nColumn {col} (char: '{formatName[col-16] if col-16 < len('formatName') else '?'}') found {len(refs)} refs")
            break
            
finally:
    ls.stop()
    print("\nLanguage server stopped")