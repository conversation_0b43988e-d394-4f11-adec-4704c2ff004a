import os
import sys
sys.path.insert(0, 'src')

from solidlsp.ls_config import Language, LanguageServerConfig
from solidlsp.ls import SolidLanguageServer
from solidlsp.ls_logger import LanguageServerLogger
import logging

# Setup
repo_path = os.path.abspath("test/resources/repos/svelte/test_repo")
config = LanguageServerConfig(Language.SVELTE)
logger = LanguageServerLogger(log_level=logging.INFO)

print(f"Creating Svelte language server for {repo_path}")
ls = SolidLanguageServer.create(config, logger, repo_path)

try:
    ls.start()
    print("Language server started\n")
    
    # Test: Find references to User interface in store.ts
    print("=== Finding references to User interface (line 2, column 18) ===")
    refs = ls.request_references(os.path.join("lib", "store.ts"), 2, 18)
    print(f"Found {len(refs)} references total:")
    for ref in refs:
        uri = ref['uri']
        line = ref['range']['start']['line'] + 1
        print(f"  - {uri}: Line {line}")
    
    svelte_refs = [ref for ref in refs if ref["uri"].endswith(".svelte")]
    print(f"\nFound {len(svelte_refs)} references in .svelte files")
    
finally:
    ls.stop()
    print("\nLanguage server stopped")