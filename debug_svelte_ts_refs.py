#!/usr/bin/env python3
"""Debug script to test TypeScript reference finding in Svelte adapter."""

import logging
from pathlib import Path

from src.solidlsp.language_servers.svelte_language_server import SvelteProxyLanguageServer
from src.solidlsp.ls_config import LanguageServerConfig

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("debug_svelte_ts_refs")

# Test paths
repo_path = Path("test/resources/repos/svelte/test_repo").absolute()
ts_file = "lib/simple.ts"

# Create Svelte language server
config = LanguageServerConfig(language="svelte", node_project_path=str(repo_path))
ls = SvelteProxyLanguageServer(config, logger, str(repo_path))

try:
    ls.start()
    
    # Test 1: Find the formatName function
    print("\n=== Test 1: Find formatName function ===")
    symbols = ls.find_symbol("formatName", ts_file, include_body=True)
    for sym in symbols:
        print(f"Found symbol: {sym['name']} at {sym['location']['path']}:{sym['location']['range']['start']['line']}")
    
    if symbols:
        # Test 2: Find references to formatName
        print("\n=== Test 2: Find references to formatName ===")
        sym_loc = symbols[0]['location']
        refs = ls.request_references(
            sym_loc['path'],
            sym_loc['range']['start']['line'],
            sym_loc['range']['start']['character']
        )
        print(f"Found {len(refs)} references:")
        for ref in refs:
            print(f"  - {ref['uri']}:{ref['range']['start']['line']+1}")
    
    # Test 3: Check if TypeScript server is ready
    print(f"\n=== Test 3: TypeScript server status ===")
    print(f"TypeScript server ready: {ls._ts_ready.is_set()}")
    print(f"TypeScript server object: {ls._ts_server is not None}")
    if ls._ts_server_error:
        print(f"TypeScript server error: {ls._ts_server_error}")
    
    # Test 4: Direct TypeScript server query
    if ls._ts_server and ls._ts_ready.is_set():
        print("\n=== Test 4: Direct TypeScript server query ===")
        try:
            # Query TypeScript server directly
            ts_refs = ls._ts_server.request_references(ts_file, 0, 16)  # formatName position
            print(f"Direct TS server query found {len(ts_refs)} references")
            for ref in ts_refs:
                print(f"  - {ref['uri']}:{ref['range']['start']['line']+1}")
        except Exception as e:
            print(f"Direct TS query failed: {e}")
    
finally:
    ls.stop()