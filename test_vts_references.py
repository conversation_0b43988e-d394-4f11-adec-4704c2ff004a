#!/usr/bin/env python3
"""
Test script to verify VTS TypeScript language server reference finding.
"""

import asyncio
import logging
from solidlsp.ls_config import LanguageServerConfig
from solidlsp.ls_logger import LanguageServerLogger
from solidlsp.language_servers.vts_language_server import VtsLanguageServer
from solidlsp.ls_config import Language

async def test_vts_references():
    """Test VTS TypeScript language server reference finding."""
    repo_path = "/Users/<USER>/MCP/serena/test/resources/repos/svelte/test_repo"
    
    # Setup logger
    logger = LanguageServerLogger(log_level=logging.INFO)
    
    # Create VTS language server
    config = LanguageServerConfig(Language.TYPESCRIPT_VTS)
    ls = VtsLanguageServer(config, logger, repo_path)
    
    try:
        print("🚀 Starting VTS TypeScript Language Server...")
        ls.start()
        print("✅ VTS server started successfully")
        
        # Test finding the formatName symbol first using document symbols
        print("\n📍 Finding formatName symbol...")
        symbols = ls.request_document_symbols("lib/utils.ts")
        print(f"Document symbols: {symbols}")

        # Find formatName in the symbols
        format_name_symbol = None
        for symbol in symbols[0] if symbols else []:
            if symbol.get("name") == "formatName":
                format_name_symbol = symbol
                break

        if format_name_symbol:
            print(f"formatName symbol found: {format_name_symbol}")
            
            # Test finding references using the symbol's position
            sel_start = format_name_symbol["selectionRange"]["start"]
            print(f"\n🔍 Finding references to formatName at line {sel_start['line']}, char {sel_start['character']}...")
            references = ls.request_references("lib/utils.ts", sel_start["line"], sel_start["character"])
            if references:
                print(f"✅ Found {len(references)} references:")
                for ref in references:
                    file_path = ref['uri'].replace(f"file://{repo_path}/", "")
                    line = ref['range']['start']['line'] + 1
                    print(f"   - {file_path}:{line}")
            else:
                print("❌ No references found")
        else:
            print("❌ formatName symbol not found in document symbols")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("\n🛑 Stopping VTS server...")
        ls.stop()
        print("✅ VTS server stopped")

if __name__ == "__main__":
    asyncio.run(test_vts_references())