#!/usr/bin/env python3
"""
Debug script to test the core symbol matching logic and identify why
TypeScript function references fail.
"""

import sys
import os
import logging
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from solidlsp.language_servers.svelte_language_server import SvelteProxyLanguageServer
from solidlsp.ls_config import LanguageServerConfig, Language
from solidlsp.ls_logger import LanguageServerLogger
from serena.symbol import LanguageServerSymbolRetriever

def test_symbol_matching():
    """Test the core symbol matching logic for TypeScript functions."""
    
    # Initialize the Svelte language server (which handles TypeScript files)
    repo_path = "/Users/<USER>/MCP/serena/test/resources/repos/svelte/test_repo"
    config = LanguageServerConfig(Language.SVELTE)
    logger = LanguageServerLogger(logging.getLogger("test"), logging.INFO)
    ls = SvelteProxyLanguageServer(config, logger, repo_path)
    
    try:
        ls.start()
        print("Language server started successfully")
        
        retriever = LanguageServerSymbolRetriever(ls)
        
        # Test 1: Can we find formatName with exact matching?
        print("\n=== Test 1: Exact matching for formatName ===")
        exact_symbols = retriever.find_by_name("formatName", substring_matching=False, within_relative_path="lib/utils.ts")
        print(f"Found {len(exact_symbols)} symbols with exact matching:")
        for sym in exact_symbols:
            print(f"  - {sym.get_name_path()} (kind={sym.kind})")
            print(f"    Location: {sym.location.relative_path}:{sym.location.line}:{sym.location.column}")
        
        # Test 2: Can we find formatName with substring matching?
        print("\n=== Test 2: Substring matching for formatName ===")
        substring_symbols = retriever.find_by_name("formatName", substring_matching=True, within_relative_path="lib/utils.ts")
        print(f"Found {len(substring_symbols)} symbols with substring matching:")
        for sym in substring_symbols:
            print(f"  - {sym.get_name_path()} (kind={sym.kind})")
            print(f"    Location: {sym.location.relative_path}:{sym.location.line}:{sym.location.column}")
        
        # Test 3: What does request_full_symbol_tree return?
        print("\n=== Test 3: Full symbol tree for lib/utils.ts ===")
        symbol_tree = ls.request_full_symbol_tree(within_relative_path="lib/utils.ts", include_body=False)
        print(f"Symbol tree contains {len(symbol_tree)} root symbols:")
        
        def print_symbols(symbols, indent=0):
            for sym in symbols:
                name = sym.get('name', 'unknown')
                kind = sym.get('kind', 'unknown')
                print(f"{'  ' * indent}- {name} (kind={kind})")
                children = sym.get('children', [])
                if children:
                    print_symbols(children, indent + 1)
        
        print_symbols(symbol_tree)
        
        # Test 4: Try find_referencing_symbols (this should now work!)
        print("\n=== Test 4: find_referencing_symbols for formatName ===")
        try:
            refs = retriever.find_referencing_symbols("formatName", "lib/utils.ts")
            print(f"Found {len(refs)} referencing symbols")
            for ref in refs:
                print(f"  - {ref.symbol.name} (kind={ref.symbol.kind}) in {ref.symbol.location.relative_path}:{ref.line}:{ref.character}")
        except Exception as e:
            print(f"find_referencing_symbols failed: {e}")
        
        # Test 5: Check if the issue is in the symbol name path
        print("\n=== Test 5: Analyzing symbol name paths ===")
        if exact_symbols:
            sym = exact_symbols[0]
            print(f"formatName symbol name path: {sym.get_name_path()}")
            print(f"formatName symbol name: {sym.name}")
            print(f"formatName symbol kind: {sym.kind}")
            print(f"formatName symbol location: {sym.location.relative_path}:{sym.location.line}:{sym.location.column}")
            
    finally:
        ls.stop()
        print("\nLanguage server stopped")

if __name__ == "__main__":
    test_symbol_matching()