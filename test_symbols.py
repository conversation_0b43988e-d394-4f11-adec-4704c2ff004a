import os
import sys
sys.path.insert(0, 'src')

from solidlsp.ls_config import Language, LanguageServerConfig
from solidlsp.ls import SolidLanguageServer
from solidlsp.ls_logger import LanguageServerLogger
import logging

# Setup
repo_path = os.path.abspath("test/resources/repos/svelte/test_repo")
config = LanguageServerConfig(Language.SVELTE)
logger = LanguageServerLogger(log_level=logging.INFO)

print(f"Creating Svelte language server for {repo_path}")
ls = SolidLanguageServer.create(config, logger, repo_path)

try:
    ls.start()
    print("Language server started\n")
    
    # Test symbol tree
    print("=== Getting full symbol tree ===")
    symbols = ls.request_full_symbol_tree()
    
    print(f"Found {len(symbols)} top-level symbols")
    for sym in symbols:
        print(f"Top-level: {sym.get('name')} (kind={sym.get('kind')}) at {sym.get('location', {}).get('relativePath')}")
    
    # Find Button.svelte symbols
    def find_button_symbols(symbols, path=""):
        for symbol in symbols:
            name = symbol.get("name", "")
            rel_path = symbol.get("location", {}).get("relativePath", "")
            kind = symbol.get("kind", "")
            
            if "Button" in name or "Button.svelte" in rel_path:
                print(f"Found: {name} (kind={kind}) at {rel_path}")
            
            # Recurse into children
            children = symbol.get("children", [])
            if children:
                find_button_symbols(children, path + "/" + name)
    
    find_button_symbols(symbols)
    
    # Also check document symbols directly
    print("\n=== Getting document symbols for Button.svelte ===")
    button_symbols = ls.request_document_symbols("lib/Button.svelte")
    print(f"Found {len(button_symbols[0])} symbols in Button.svelte")
    for sym in button_symbols[0]:
        print(f"  - {sym.get('name')} (kind={sym.get('kind')})")
    
    # Check if any symbol is named "Button"
    has_button = any(sym.get('name') == 'Button' for sym in button_symbols[0])
    print(f"\nHas symbol named 'Button': {has_button}")
    
finally:
    ls.stop()
    print("\nLanguage server stopped")