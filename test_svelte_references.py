#!/usr/bin/env python3

import sys
import os
import logging
from pathlib import Path

# Add the src directory to the Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from solidlsp.language_servers.svelte_language_server import SvelteLanguageServer
from solidlsp.ls import LanguageServerConfig, LanguageServerLogger
from solidlsp.ls_config import Language

def test_svelte_references():
    """Test that the Svelte language server can find references for TypeScript symbols"""
    
    # Setup
    repository_root = "/Users/<USER>/MCP/serena/test/resources/repos/svelte/test_repo"
    
    # Create logger
    logger = LanguageServerLogger(json_format=False, log_level=logging.DEBUG)
    
    # Create config
    config = LanguageServerConfig(
        code_language=Language.SVELTE
    )
    
    # Create Svelte language server
    svelte_server = SvelteLanguageServer(config, logger, repository_root)
    
    try:
        # Start the server
        print("Starting Svelte language server...")
        svelte_server.start()
        
        # Test 1: Find references to formatName function in utils.ts
        print("\n=== Testing formatName references ===")
        references = svelte_server.request_referencing_symbols(
            relative_file_path="lib/utils.ts",
            line=2,  # formatName function definition (0-indexed, line 3 in file)
            column=16,  # position of function name
            include_imports=True,
            include_self=False
        )
        
        print(f"Found {len(references)} references to formatName:")
        for ref in references:
            symbol = ref.symbol
            print(f"  - {symbol['name']} ({symbol['kind']}) in {symbol.get('location', {}).get('relativePath', 'unknown')} at line {ref.line}")
        
        # Test 2: Find references to User interface in store.ts
        print("\n=== Testing User interface references ===")
        references = svelte_server.request_referencing_symbols(
            relative_file_path="lib/store.ts",
            line=1,  # User interface definition (0-indexed, line 2 in file)
            column=17,  # position of interface name
            include_imports=True,
            include_self=False
        )
        
        print(f"Found {len(references)} references to User:")
        for ref in references:
            symbol = ref.symbol
            print(f"  - {symbol['name']} ({symbol['kind']}) in {symbol.get('location', {}).get('relativePath', 'unknown')} at line {ref.line}")
            
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Clean up
        try:
            svelte_server.stop()
        except:
            pass

if __name__ == "__main__":
    test_svelte_references()