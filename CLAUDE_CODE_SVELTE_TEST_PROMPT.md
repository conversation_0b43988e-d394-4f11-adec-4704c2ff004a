# SvelteKit Adapter Test Prompt for Claude Code

**Paste this prompt directly into Claude Code:**

---

I need you to validate the Serena MCP SvelteKit adapter on this project. Test cross-language intelligence between TypeScript and Svelte files.

**Step 1: Project Overview**
```
mcp__serena__get_current_config
mcp__serena__get_symbols_overview relative_path='src'
```

**Step 2: Find Key Symbols**
Look for important symbols like auth objects, utility functions, or interfaces:
```
mcp__serena__find_symbol name_path='auth' relative_path='src/lib'
mcp__serena__find_symbol name_path='User' substring_matching=true
```

**Step 3: Test Cross-Language References (CRITICAL)**
Pick a TypeScript symbol that should be used in Svelte files:
```
mcp__serena__find_referencing_symbols name_path='[SYMBOL_NAME]' relative_path='[PATH_TO_TS_FILE]'
```

**Expected Results:**
- Should find references in BOTH .ts AND .svelte files
- Should return actual file paths and line numbers
- Should NOT return empty arrays if references exist

**Step 4: Test SvelteKit-Specific**
```
mcp__serena__find_symbol name_path='PageProps' 
mcp__serena__list_dir relative_path='src/routes'
```

**Report Format:**
- ✅ PASS: Found X references across .ts and .svelte files
- ❌ FAIL: Empty results or missing expected references

**Key Test**: The most important test is `find_referencing_symbols` - this was broken before and should now work correctly. If it returns empty arrays for symbols that clearly have references, that's a critical failure.

Focus on symbols that are imported/used across multiple files, especially between .ts and .svelte files.