export function formatName(firstName: string, lastName: string): string {
  return `${firstName} ${lastName}`;
}

export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export interface Config {
  apiUrl: string;
  timeout: number;
  retries: number;
}

export const defaultConfig: Config = {
  apiUrl: 'https://api.example.com',
  timeout: 5000,
  retries: 3
};

export class ApiClient {
  private config: Config;

  constructor(config: Config = defaultConfig) {
    this.config = config;
  }

  async get(endpoint: string): Promise<any> {
    // Mock implementation
    return Promise.resolve({ data: 'mock data' });
  }

  async post(endpoint: string, data: any): Promise<any> {
    // Mock implementation
    return Promise.resolve({ success: true });
  }
}