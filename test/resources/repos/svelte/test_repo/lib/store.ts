import { writable } from 'svelte/store';

export const count = writable(0);

export interface User {
  id: number;
  name: string;
  email: string;
}

export const currentUser = writable<User | null>(null);

export function incrementCount(): void {
  count.update(n => n + 1);
}

export function resetCount(): void {
  count.set(0);
}

export class UserManager {
  private users: User[] = [];

  addUser(user: User): void {
    this.users.push(user);
  }

  getUser(id: number): User | undefined {
    return this.users.find(user => user.id === id);
  }

  getAllUsers(): User[] {
    return [...this.users];
  }
}