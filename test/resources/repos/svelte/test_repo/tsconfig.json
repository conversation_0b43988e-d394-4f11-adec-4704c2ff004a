{
  "compilerOptions": {
    "target": "ESNext",
    "useDefineForClassFields": true,
    "lib": ["DOM", "DOM.Iterable", "ESNext"],
    "allowJs": true,
    "checkJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "strict": true
  },
  "include": [
    /* Your include paths are great and comprehensive */
    "src/**/*.d.ts",
    "src/**/*.ts",
    "src/**/*.js",
    "src/**/*.svelte",
    "lib/**/*.d.ts",
    "lib/**/*.ts",
    "lib/**/*.js",
    "lib/**/*.svelte",
    "*.svelte",
    "**/*.svelte"
  ]
  /* "references" is removed for simplicity in this context */
}