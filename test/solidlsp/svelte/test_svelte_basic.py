import os

import pytest

from solidlsp import SolidLanguageServer
from solidlsp.ls_config import Language
from solidlsp.ls_utils import SymbolUtils


@pytest.mark.svelte
class TestSvelteLanguageServer:
    @pytest.mark.parametrize("language_server", [Language.SVELTE], indirect=True)
    def test_find_symbol(self, language_server: SolidLanguageServer) -> None:
        symbols = language_server.request_full_symbol_tree()
        # Test Svelte component symbols
        assert SymbolUtils.symbol_tree_contains_name(symbols, "App"), "App component not found in symbol tree"
        assert SymbolUtils.symbol_tree_contains_name(symbols, "Button"), "Button component not found in symbol tree"
        assert SymbolUtils.symbol_tree_contains_name(symbols, "handleClick"), "handleClick function not found in symbol tree"

        # Test that some TypeScript symbols are detected (limited support)
        # Note: This tests the actual capability, not full TypeScript support
        assert SymbolUtils.symbol_tree_contains_name(symbols, "count"), "count store not found in symbol tree"
        assert SymbolUtils.symbol_tree_contains_name(symbols, "User"), "User interface not found in symbol tree"

    @pytest.mark.parametrize("language_server", [Language.SVELTE], indirect=True)
    def test_document_symbols(self, language_server: SolidLanguageServer) -> None:
        """Test that document symbols work for Svelte files"""
        file_path = os.path.join("App.svelte")
        symbols, roots = language_server.request_document_symbols(file_path)

        # Should find various symbols in App.svelte
        symbol_names = [sym.get("name") for sym in symbols]
        assert "handleClick" in symbol_names, "Should find handleClick function"
        assert "handleIncrement" in symbol_names, "Should find handleIncrement function"
        assert "handleReset" in symbol_names, "Should find handleReset function"

        # Check that symbols have proper kinds
        handleClick_symbol = next((sym for sym in symbols if sym.get("name") == "handleClick"), None)
        assert handleClick_symbol is not None
        assert handleClick_symbol.get("kind") == 12, "handleClick should be a Function (kind 12)"

    @pytest.mark.parametrize("language_server", [Language.SVELTE], indirect=True)
    def test_button_component_symbols(self, language_server: SolidLanguageServer) -> None:
        """Test that Button component symbols are properly detected"""
        file_path = os.path.join("lib", "Button.svelte")
        symbols, roots = language_server.request_document_symbols(file_path)

        # Should find the handleClick function in Button.svelte
        symbol_names = [sym.get("name") for sym in symbols]
        assert "handleClick" in symbol_names, "Should find handleClick function in Button.svelte"

    @pytest.mark.parametrize("language_server", [Language.SVELTE], indirect=True)
    def test_typescript_file_limitations(self, language_server: SolidLanguageServer) -> None:
        """Test the actual limitations of TypeScript support in Svelte LS"""
        # Test if Svelte LS can read .ts files (it shouldn't reliably)
        try:
            # Try to read a TypeScript file directly
            symbols, roots = language_server.request_document_symbols("lib/store.js")
            ts_symbols = [sym.get("name") for sym in symbols]
            
            # Svelte LS has very limited .ts file support
            # This test documents the actual behavior rather than expected behavior
            print(f"TypeScript file symbols found by Svelte LS: {len(ts_symbols)}")
            
        except Exception as e:
            # Expected: Svelte LS likely cannot properly parse standalone .ts files
            print(f"Expected limitation: Svelte LS cannot read .ts files: {e}")
            
        # The key insight: Svelte LS is designed for .svelte files with embedded TypeScript,
        # NOT for standalone .ts files or cross-file TypeScript analysis
        """Test and document TypeScript support limitations"""
        # Test that we can get limited document symbols from .ts files
        file_path = os.path.join("lib", "store.ts")
        try:
            symbols, roots = language_server.request_document_symbols(file_path)
            symbol_names = [sym.get("name") for sym in symbols]
            # Limited support - may only find some symbols
            assert len(symbol_names) > 0, "Should find at least some symbols in TypeScript files"
        except Exception:
            # Document that this may not work reliably
            pass

        # Test that cross-references between .ts and .svelte files don't work
        svelte_file = "App.svelte"
        try:
            # Try to find references to formatName (used in template but defined in utils.ts)
            references = language_server.request_references(svelte_file, 59, 15)
            # This should return 0 or very limited results
            assert len(references or []) == 0, "Cross-references between .ts and .svelte files are not supported"
        except Exception:
            # Expected - cross-references may not work
            pass
