"""
Test suite for verifying Svelte language server cross-file references between .svelte and .ts files.

This test validates that the Svelte proxy language server correctly handles:
1. Go to definition from .svelte to .ts files
2. Find references from .ts files to .svelte files
3. Symbol navigation across file boundaries
4. Virtual TSX file generation and mapping
"""

import os

import pytest

from solidlsp import SolidLanguageServer
from solidlsp.ls_config import Language
from solidlsp.ls_utils import SymbolUtils


@pytest.mark.svelte
class TestSvelteCrossFileReferences:
    """Tests specifically for cross-file reference functionality between Svelte and TypeScript files."""

    @pytest.mark.parametrize("language_server", [Language.SVELTE], indirect=True)
    def test_definition_from_svelte_to_ts(self, language_server: SolidLanguageServer) -> None:
        """Test go to definition from Svelte component to TypeScript file."""
        # Test formatName import in Button.svelte (line 2)
        definitions = language_server.request_definition(os.path.join("lib", "Button.svelte"), 1, 10)
        assert len(definitions) > 0, "Should find definition for formatName import"

        # Verify the definition is in utils.ts
        def_location = definitions[0]
        assert def_location["uri"].endswith("lib/utils.ts"), f"Definition should be in utils.ts, found: {def_location['uri']}"

        # Test usage of formatName function in Button.svelte (line 23)
        definitions = language_server.request_definition(os.path.join("lib", "Button.svelte"), 22, 35)
        assert len(definitions) > 0, "Should find definition for formatName usage"

        def_location = definitions[0]
        assert def_location["uri"].endswith("lib/utils.ts"), f"Definition should be in utils.ts, found: {def_location['uri']}"
        assert def_location["range"]["start"]["line"] == 2, "Should point to formatName function definition"

    @pytest.mark.parametrize("language_server", [Language.SVELTE], indirect=True)
    def test_type_definition_from_svelte_to_ts(self, language_server: SolidLanguageServer) -> None:
        """Test go to definition for TypeScript types imported in Svelte."""
        # Test User type import in Button.svelte (line 3)
        # Column 16 points to 'User' in 'import type { User } from'
        definitions = language_server.request_definition(os.path.join("lib", "Button.svelte"), 2, 16)
        assert len(definitions) > 0, "Should find definition for User type"

        def_location = definitions[0]
        assert def_location["uri"].endswith("lib/store.ts"), f"Definition should be in store.ts, found: {def_location['uri']}"
        assert def_location["range"]["start"]["line"] == 1, "Should point to User interface definition"

    @pytest.mark.parametrize("language_server", [Language.SVELTE], indirect=True)
    def test_references_from_ts_to_svelte(self, language_server: SolidLanguageServer) -> None:
        """Test find references from TypeScript file to Svelte components."""
        # Find references to User interface defined in store.ts
        references = language_server.request_references(os.path.join("lib", "store.ts"), 2, 18)

        # Should find references in both .ts and .svelte files
        svelte_refs = [ref for ref in references if ref["uri"].endswith(".svelte")]

        assert len(svelte_refs) > 0, "Should find references to User type in Svelte files"
        assert any("Button.svelte" in ref["uri"] for ref in svelte_refs), "Should find User reference in Button.svelte"

    @pytest.mark.parametrize("language_server", [Language.SVELTE], indirect=True)
    def test_references_for_exported_function(self, language_server: SolidLanguageServer) -> None:
        """Test find references for a function exported from TS and used in Svelte."""
        # Find references to formatName function in utils.ts
        references = language_server.request_references(os.path.join("lib", "utils.ts"), 2, 17)

        # Should find the declaration and usage in Button.svelte
        assert len(references) >= 2, f"Should find at least 2 references (declaration + usage), found {len(references)}"

        svelte_refs = [ref for ref in references if ref["uri"].endswith("Button.svelte")]
        assert len(svelte_refs) > 0, "Should find formatName usage in Button.svelte"

    @pytest.mark.parametrize("language_server", [Language.SVELTE], indirect=True)
    def test_class_references_across_files(self, language_server: SolidLanguageServer) -> None:
        """Test find references for a class defined in TS."""
        # Find references to ApiClient class in utils.ts
        references = language_server.request_references(os.path.join("lib", "utils.ts"), 27, 13)

        # Verify we get the class definition location
        assert len(references) >= 1, "Should find at least the class definition"

        # Test class method references
        references = language_server.request_references(os.path.join("lib", "utils.ts"), 30, 14)
        assert len(references) >= 1, "Should find method definition"

    @pytest.mark.parametrize("language_server", [Language.SVELTE], indirect=True)
    def test_symbol_tree_includes_both_file_types(self, language_server: SolidLanguageServer) -> None:
        """Test that symbol tree correctly includes symbols from both .svelte and .ts files."""
        symbols = language_server.request_full_symbol_tree()

        # Verify TypeScript symbols
        assert SymbolUtils.symbol_tree_contains_name(symbols, "formatName"), "formatName function not found"
        assert SymbolUtils.symbol_tree_contains_name(symbols, "User"), "User interface not found"
        assert SymbolUtils.symbol_tree_contains_name(symbols, "ApiClient"), "ApiClient class not found"
        assert SymbolUtils.symbol_tree_contains_name(symbols, "UserManager"), "UserManager class not found"

        # Verify Svelte component symbols
        assert SymbolUtils.symbol_tree_contains_name(symbols, "Button"), "Button component not found"
        assert SymbolUtils.symbol_tree_contains_name(symbols, "App"), "App component not found"

    @pytest.mark.parametrize("language_server", [Language.SVELTE], indirect=True)
    def test_virtual_tsx_file_handling(self, language_server: SolidLanguageServer) -> None:
        """Test that virtual TSX files are properly created and managed."""
        # Open a Svelte file and verify it gets transformed
        with language_server.open_file(os.path.join("lib", "Button.svelte")) as buffer:
            # The file should be open
            assert buffer is not None
            assert buffer.uri.endswith("Button.svelte")

            # Try to get definition while file is open (should use virtual TSX)
            definitions = language_server.request_definition(os.path.join("lib", "Button.svelte"), 22, 35)
            assert len(definitions) > 0, "Should find definition through virtual TSX file"

    @pytest.mark.parametrize("language_server", [Language.SVELTE], indirect=True)
    def test_bidirectional_navigation(self, language_server: SolidLanguageServer) -> None:
        """Test navigation works both ways: Svelte->TS and TS->Svelte."""
        # First, go from Svelte to TS (formatName usage to definition)
        svelte_to_ts_defs = language_server.request_definition(os.path.join("lib", "Button.svelte"), 22, 35)
        assert len(svelte_to_ts_defs) > 0

        ts_def = svelte_to_ts_defs[0]
        assert ts_def["uri"].endswith("lib/utils.ts")

        # Now find references from the TS definition back to Svelte
        ts_line = ts_def["range"]["start"]["line"]
        ts_col = ts_def["range"]["start"]["character"]

        ts_to_svelte_refs = language_server.request_references(os.path.join("lib", "utils.ts"), ts_line, ts_col)

        # Should find references including the one in Button.svelte
        svelte_refs = [ref for ref in ts_to_svelte_refs if ref["uri"].endswith("Button.svelte")]
        assert len(svelte_refs) > 0, "Should find reference back to Button.svelte"
