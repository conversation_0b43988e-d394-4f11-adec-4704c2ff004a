"""Tests for Svelte support file functionality."""

from pathlib import Path

import pytest

from serena.config.serena_config import Language, ProjectConfig
from serena.project import Project
from solidlsp.language_servers.svelte_language_server import SvelteProxyLanguageServer
from solidlsp.ls_config import LanguageServerConfig


@pytest.mark.svelte
class TestSvelteSupportFiles:
    """Test support file functionality for Svelte language server."""

    @pytest.fixture
    def test_repo_path(self):
        """Get the path to the Svelte test repository."""
        return Path(__file__).parent.parent.parent / "resources/repos/svelte/test_repo"

    @pytest.fixture
    def svelte_ls(self, test_repo_path):
        """Create a Svelte language server instance."""
        import logging

        from solidlsp.ls_logger import LanguageServerLogger

        config = LanguageServerConfig(Language.SVELTE)
        logger = LanguageServerLogger("svelte_test", logging.INFO)
        ls = SvelteProxyLanguageServer(config, logger, str(test_repo_path))
        yield ls
        if hasattr(ls, "stop"):
            ls.stop()

    @pytest.fixture
    def project(self, test_repo_path):
        """Create a project instance."""
        config = ProjectConfig(project_name="svelte_test", language=Language.SVELTE, ignored_paths=[], ignore_all_files_in_gitignore=True)
        return Project(str(test_repo_path), config)

    def test_get_support_files(self, svelte_ls):
        """Test that get_support_files returns expected patterns."""
        patterns = svelte_ls.get_support_files()
        assert ".svelte-kit/**/*.d.ts" in patterns
        assert ".svelte-kit/ambient.d.ts" in patterns
        assert ".svelte-kit/tsconfig.json" in patterns

    def test_is_support_file(self, svelte_ls):
        """Test that is_support_file correctly identifies support files."""
        # Support files
        assert svelte_ls.is_support_file(".svelte-kit/types/src/routes/$types.d.ts")
        assert svelte_ls.is_support_file(".svelte-kit/ambient.d.ts")
        assert svelte_ls.is_support_file(".svelte-kit/tsconfig.json")
        assert svelte_ls.is_support_file(".svelte-kit/types/app.d.ts")

        # Non-support files
        assert not svelte_ls.is_support_file("src/routes/+page.svelte")
        assert not svelte_ls.is_support_file("node_modules/svelte/index.js")
        assert not svelte_ls.is_support_file("package.json")

    def test_validate_path_read_access(self, project, svelte_ls):
        """Test that support files can be read."""
        # Should not raise
        project.validate_relative_path(".svelte-kit/types/src/routes/$types.d.ts", svelte_ls, write=False)

    def test_validate_path_write_protection(self, project, svelte_ls):
        """Test that support files are write-protected."""
        with pytest.raises(ValueError, match="read-only framework support file"):
            project.validate_relative_path(".svelte-kit/types/src/routes/$types.d.ts", svelte_ls, write=True)

    def test_node_modules_still_blocked(self, project, svelte_ls):
        """Test that node_modules is still blocked even with language server."""
        with pytest.raises(ValueError, match="ignored"):
            project.validate_relative_path("node_modules/svelte/index.js", svelte_ls, write=False)

    def test_filter_support_files(self, project, svelte_ls):
        """Test that filter_support_files correctly filters support files."""
        paths = [
            "src/routes/+page.svelte",
            ".svelte-kit/types/src/routes/$types.d.ts",
            ".svelte-kit/ambient.d.ts",
            "src/lib/index.ts",
            ".svelte-kit/tsconfig.json",
        ]

        # Without support files
        filtered = project.filter_support_files(paths, svelte_ls, include_support=False)
        assert "src/routes/+page.svelte" in filtered
        assert "src/lib/index.ts" in filtered
        assert ".svelte-kit/types/src/routes/$types.d.ts" not in filtered
        assert ".svelte-kit/ambient.d.ts" not in filtered
        assert ".svelte-kit/tsconfig.json" not in filtered

        # With support files
        with_support = project.filter_support_files(paths, svelte_ls, include_support=True)
        assert set(with_support) == set(paths)

    def test_svelte_files_not_ignored(self, svelte_ls):
        """Test that .svelte files are never ignored."""
        # Regular Svelte component files should not be ignored
        assert not svelte_ls.is_ignored_path("App.svelte")
        assert not svelte_ls.is_ignored_path("lib/Button.svelte")

        # Even with ignore_unsupported_files=True (the default)
        assert not svelte_ls.is_ignored_path("App.svelte", ignore_unsupported_files=True)

    def test_support_files_not_in_symbol_tree(self, svelte_ls):
        """Test that support files don't appear in symbol tree."""
        # This would require starting the language server and checking
        # that .svelte-kit files don't appear in request_full_symbol_tree
        # We'll skip the actual test for now as it requires a running LS
