import pytest

from solidlsp.ls import Language, SolidLanguageServer
from solidlsp.ls_utils import SymbolUtils


@pytest.mark.typescript
class TestCompareTypeScriptVsSvelte:
    """
    Compare TypeScript and Svelte language servers on the same codebase
    to understand the differences in capabilities.
    """

    @pytest.mark.parametrize("language_server", [Language.TYPESCRIPT], indirect=True)
    def test_typescript_ls_on_mixed_project(self, language_server: SolidLanguageServer) -> None:
        """Test how TypeScript LS handles the mixed Svelte/TypeScript project"""
        try:
            # Get full symbol tree with TypeScript LS
            symbols = language_server.request_full_symbol_tree()

            # Look for TypeScript symbols
            ts_symbols_found = []
            if SymbolUtils.symbol_tree_contains_name(symbols, "count"):
                ts_symbols_found.append("count")
            if SymbolUtils.symbol_tree_contains_name(symbols, "User"):
                ts_symbols_found.append("User")
            if SymbolUtils.symbol_tree_contains_name(symbols, "UserManager"):
                ts_symbols_found.append("UserManager")
            if SymbolUtils.symbol_tree_contains_name(symbols, "formatName"):
                ts_symbols_found.append("formatName")
            if SymbolUtils.symbol_tree_contains_name(symbols, "ApiClient"):
                ts_symbols_found.append("ApiClient")

            print(f"TypeScript LS found symbols: {ts_symbols_found}")

            # Try to get document symbols for .ts files
            for ts_file in ["lib/store.ts", "lib/utils.ts"]:
                try:
                    ts_doc_symbols, ts_roots = language_server.request_document_symbols(ts_file)
                    ts_doc_symbol_names = [sym.get("name") for sym in ts_doc_symbols]
                    print(f"TypeScript LS document symbols from {ts_file}: {ts_doc_symbol_names}")
                except Exception as e:
                    print(f"TypeScript LS failed to get document symbols from {ts_file}: {e}")

            # Try to get document symbols for .svelte files (should fail or be limited)
            try:
                svelte_doc_symbols, svelte_roots = language_server.request_document_symbols("App.svelte")
                svelte_symbol_names = [sym.get("name") for sym in svelte_doc_symbols]
                print(f"TypeScript LS document symbols from App.svelte: {svelte_symbol_names}")
            except Exception as e:
                print(f"TypeScript LS cannot analyze App.svelte: {e}")

        except Exception as e:
            print(f"Error testing TypeScript LS on mixed project: {e}")

    @pytest.mark.parametrize("language_server", [Language.TYPESCRIPT], indirect=True)
    def test_typescript_cross_references(self, language_server: SolidLanguageServer) -> None:
        """Test cross-references with TypeScript LS"""
        try:
            # Test references to formatName function
            file_path = "lib/utils.ts"
            doc_symbols, roots = language_server.request_document_symbols(file_path)
            formatName_symbol = next((sym for sym in doc_symbols if sym.get("name") == "formatName"), None)

            if formatName_symbol and "range" in formatName_symbol:
                line = formatName_symbol["range"]["start"]["line"]
                character = formatName_symbol["range"]["start"]["character"]

                references = language_server.request_references(file_path, line, character)
                print(f"TypeScript LS references to formatName: {len(references) if references else 0}")

                if references:
                    for ref in references:
                        print(f"  TypeScript LS reference at: {ref}")

        except Exception as e:
            print(f"Error testing TypeScript LS cross-references: {e}")

    def test_comparison_summary(self) -> None:
        """Summarize the comparison between TypeScript and Svelte language servers"""
        print("\n" + "=" * 70)
        print("COMPARISON: TYPESCRIPT LS vs SVELTE LS ON MIXED PROJECT")
        print("=" * 70)
        print("This test shows how each language server handles the same codebase")
        print("with both .ts and .svelte files.")
        print("=" * 70)
