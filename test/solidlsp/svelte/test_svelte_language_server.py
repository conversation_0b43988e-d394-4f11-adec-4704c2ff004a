"""
Comprehensive test suite for the Svelte Proxy language server adapter.

This suite validates the dual-server proxy architecture, ensuring full support for:
- Svelte components (.svelte)
- Standalone TypeScript/JavaScript files (.ts, .js)
- Critical cross-language features like "go to definition" and "find references".
"""

import os

import pytest

from solidlsp import SolidLanguageServer
from solidlsp.ls_config import Language
from solidlsp.ls_utils import SymbolUtils


@pytest.mark.svelte
class TestSvelteProxyLanguageServer:
    """Validates Svelte and TypeScript integration via the Svelte Proxy Language Server."""

    @pytest.mark.parametrize("language_server", [Language.SVELTE], indirect=True)
    def test_symbol_tree_discovery(self, language_server: SolidLanguageServer) -> None:
        """
        Tests that the merged symbol tree correctly discovers symbols from both
        Svelte components and standalone TypeScript files.
        """
        symbols = language_server.request_full_symbol_tree()

        # Verify symbols from Svelte components are present
        assert SymbolUtils.symbol_tree_contains_name(symbols, "App"), "App component not found"
        assert SymbolUtils.symbol_tree_contains_name(symbols, "Button"), "Button component not found"

        # Verify symbols from standalone TypeScript files are present
        assert SymbolUtils.symbol_tree_contains_name(symbols, "User"), "User interface not found"
        assert SymbolUtils.symbol_tree_contains_name(symbols, "formatName"), "formatName function not found"
        assert SymbolUtils.symbol_tree_contains_name(symbols, "ApiClient"), "ApiClient class not found"

    @pytest.mark.parametrize("language_server", [Language.SVELTE], indirect=True)
    def test_cross_language_definition_and_references(self, language_server: SolidLanguageServer) -> None:
        """
        Crucially, tests that "go to definition" and "find references" work
        correctly across .svelte and .ts file boundaries via the proxy.
        """
        # 1. Test "go to definition" from a .svelte file to a .ts file.
        #    Targeting the usage of `formatName` in `Button.svelte`.
        #    LSP is 0-indexed: line 23 is 22, column 25 is 24.
        definitions = language_server.request_definition(os.path.join("lib", "Button.svelte"), 22, 24)
        assert len(definitions) > 0, "Should find at least one definition for 'formatName'"

        definition = definitions[0]
        def_path = definition.get("uri", "")
        assert def_path.endswith("lib/utils.ts"), f"Definition should be in utils.ts, but was in {def_path}"

        # 2. Test "find references" for a TypeScript type used in Svelte components.
        #    Targeting the definition of the `User` interface in `store.ts`.
        #    LSP is 0-indexed: line 3 is 2, column 19 is 18.
        references = language_server.request_references(os.path.join("lib", "store.ts"), 2, 18)
        assert len(references) >= 2, "Should find references to User interface in at least two Svelte files"

        ref_paths = {ref.get("uri", "") for ref in references}
        assert any("Button.svelte" in path for path in ref_paths), "Should find a reference in Button.svelte"
        assert any("App.svelte" in path for path in ref_paths), "Should find a reference in App.svelte"
