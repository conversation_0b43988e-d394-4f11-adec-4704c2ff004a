import os

import pytest

from solidlsp.ls import Language, SolidLanguageServer
from solidlsp.ls_utils import SymbolUtils


@pytest.mark.svelte
class TestSvelteTypeScriptCapabilities:
    """
    Test the actual TypeScript capabilities of the Svelte language server.
    This tests whether the Svelte LS can handle .ts files and cross-references
    between .ts and .svelte files, as questioned by the maintainer.
    """

    @pytest.mark.parametrize("language_server", [Language.SVELTE], indirect=True)
    def test_typescript_file_symbol_detection(self, language_server: SolidLanguageServer) -> None:
        """Test if Svelte LS can find symbols in pure .ts files"""
        try:
            # Test if the language server can find symbols in store.ts
            symbols = language_server.request_full_symbol_tree()

            # Look for TypeScript symbols from store.ts
            ts_symbols_found = []
            if SymbolUtils.symbol_tree_contains_name(symbols, "count"):
                ts_symbols_found.append("count")
            if SymbolUtils.symbol_tree_contains_name(symbols, "User"):
                ts_symbols_found.append("User")
            if SymbolUtils.symbol_tree_contains_name(symbols, "UserManager"):
                ts_symbols_found.append("UserManager")
            if SymbolUtils.symbol_tree_contains_name(symbols, "incrementCount"):
                ts_symbols_found.append("incrementCount")

            print(f"TypeScript symbols found in full symbol tree: {ts_symbols_found}")

            # Try to get document symbols specifically for the .ts file
            try:
                file_path = os.path.join("lib", "store.ts")
                ts_doc_symbols, ts_roots = language_server.request_document_symbols(file_path)
                ts_doc_symbol_names = [sym.get("name") for sym in ts_doc_symbols]
                print(f"Document symbols from store.ts: {ts_doc_symbol_names}")
            except Exception as e:
                print(f"Failed to get document symbols from store.ts: {e}")
                ts_doc_symbols = []

        except Exception as e:
            print(f"Error testing TypeScript file symbol detection: {e}")
            # This tells us the Svelte LS cannot handle .ts files directly
            ts_symbols_found = []
            ts_doc_symbols = []

        # Document our findings
        if not ts_symbols_found and not ts_doc_symbols:
            print("FINDING: Svelte LS cannot analyze pure .ts files directly")
        else:
            print("FINDING: Svelte LS can analyze .ts files to some degree")

    @pytest.mark.parametrize("language_server", [Language.SVELTE], indirect=True)
    def test_cross_reference_ts_to_svelte(self, language_server: SolidLanguageServer) -> None:
        """Test cross-references from .ts files to .svelte files"""
        try:
            # Try to find references to formatName function (defined in utils.ts, used in Button.svelte)
            file_path = os.path.join("lib", "utils.ts")

            # Find the formatName function location first
            try:
                doc_symbols, roots = language_server.request_document_symbols(file_path)
                formatName_symbol = next((sym for sym in doc_symbols if sym.get("name") == "formatName"), None)

                if formatName_symbol and "range" in formatName_symbol:
                    line = formatName_symbol["range"]["start"]["line"]
                    character = formatName_symbol["range"]["start"]["character"]

                    # Try to find references to formatName
                    references = language_server.request_references(file_path, line, character)
                    print(f"References to formatName: {len(references) if references else 0}")

                    if references:
                        for ref in references:
                            print(f"  Reference at: {ref}")
                            # Check if any references are in .svelte files
                            if ref.get("uri", "").endswith(".svelte"):
                                print("FINDING: Found cross-reference from .ts to .svelte file")
                                return

                    print("FINDING: No cross-references found from .ts to .svelte files")
                else:
                    print("Could not find formatName symbol in utils.ts")

            except Exception as e:
                print(f"Error finding formatName symbol: {e}")

        except Exception as e:
            print(f"Error testing cross-references from .ts to .svelte: {e}")
            print("FINDING: Cannot test cross-references from .ts files")

    @pytest.mark.parametrize("language_server", [Language.SVELTE], indirect=True)
    def test_cross_reference_svelte_to_ts(self, language_server: SolidLanguageServer) -> None:
        """Test cross-references from .svelte files to .ts files"""
        try:
            # Try to find references from Button.svelte to utils.ts
            file_path = "App.svelte"

            # Look for the import of formatName in App.svelte
            # We need to find where formatName is used/imported
            doc_symbols, roots = language_server.request_document_symbols(file_path)
            print(f"App.svelte symbols: {[sym.get('name') for sym in doc_symbols]}")

            # Try to find references at the import line (line 4, where formatName is imported)
            try:
                references = language_server.request_references(file_path, 59, 15)  # formatName usage in template
                print(f"References from App.svelte formatName usage: {len(references) if references else 0}")

                if references:
                    for ref in references:
                        print(f"  Reference at: {ref}")
                        # Check if any references point to .ts files
                        if ref.get("uri", "").endswith(".ts"):
                            print("FINDING: Found cross-reference from .svelte to .ts file")
                            return

                print("FINDING: No cross-references found from .svelte to .ts files")

            except Exception as e:
                print(f"Error finding references from formatName usage: {e}")

        except Exception as e:
            print(f"Error testing cross-references from .svelte to .ts: {e}")
            print("FINDING: Cannot test cross-references from .svelte files")

    @pytest.mark.parametrize("language_server", [Language.SVELTE], indirect=True)
    def test_typescript_intellisense_in_svelte(self, language_server: SolidLanguageServer) -> None:
        """Test if TypeScript intellisense works within .svelte files"""
        try:
            file_path = "App.svelte"

            # Test completion at a location where we use TypeScript imports
            # Line 12: const userManager = new UserManager();
            # Let's try to get completions after "userManager."
            try:
                completions = language_server.request_completions(file_path, 12, 30)  # After "const userManager = new "
                if completions:
                    completion_labels = [comp.get("label", "") for comp in completions]
                    print(f"Completions after 'new ': {completion_labels[:10]}")  # Show first 10

                    if "UserManager" in completion_labels:
                        print("FINDING: TypeScript class completion works in .svelte files")
                    else:
                        print("FINDING: TypeScript class completion may not work in .svelte files")
                else:
                    print("No completions found")

            except Exception as e:
                print(f"Error testing completions: {e}")

        except Exception as e:
            print(f"Error testing TypeScript intellisense in .svelte: {e}")

    @pytest.mark.parametrize("language_server", [Language.SVELTE], indirect=True)
    def test_definition_navigation_ts_svelte(self, language_server: SolidLanguageServer) -> None:
        """Test go-to-definition from .svelte to .ts files"""
        try:
            file_path = "App.svelte"

            # Try to go to definition of formatName (imported from utils.ts)
            # Look for the import line: import { formatName, ... } from './lib/utils';
            try:
                definition = language_server.request_definition(file_path, 4, 15)  # formatName in import
                print(f"Definition for formatName import: {definition}")

                if definition and len(definition) > 0:
                    def_uri = definition[0].get("uri", "")
                    if def_uri.endswith("utils.ts"):
                        print("FINDING: Go-to-definition works from .svelte to .ts files")
                    else:
                        print(f"FINDING: Go-to-definition points to unexpected file: {def_uri}")
                else:
                    print("FINDING: Go-to-definition does not work from .svelte to .ts files")

            except Exception as e:
                print(f"Error testing go-to-definition: {e}")

        except Exception as e:
            print(f"Error testing definition navigation: {e}")

    def test_summarize_findings(self) -> None:
        """Summarize what we learned about Svelte LS TypeScript capabilities"""
        print("\n" + "=" * 60)
        print("SUMMARY OF SVELTE LANGUAGE SERVER TYPESCRIPT CAPABILITIES")
        print("=" * 60)
        print("This test suite evaluates the actual TypeScript capabilities")
        print("of the Svelte Language Server, as questioned by the maintainer.")
        print("The individual test methods above will show specific findings.")
        print("=" * 60)
