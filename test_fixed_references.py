#!/usr/bin/env python3

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from solidlsp.ls_config import LanguageServerConfig
from solidlsp.ls_logger import LanguageServerLogger
from solidlsp.language_servers.svelte_language_server import SvelteProxyLanguageServer
from serena.symbol import LanguageServerSymbolRetriever

def test_fixed_references():
    # Setup
    repo_path = "/Users/<USER>/MCP/serena/test/resources/repos/svelte/test_repo"
    config = LanguageServerConfig(code_language="svelte")
    logger = LanguageServerLogger()
    
    # Create language server
    ls = SvelteProxyLanguageServer.create(config, logger, repo_path)
    
    try:
        ls.start()
        print("Language server started successfully")
        
        # Test the retriever's find_referencing_symbols method
        print("\n=== Testing find_referencing_symbols for formatName ===")
        retriever = LanguageServerSymbolRetriever(ls)
        
        # Try to find references to formatName
        references = retriever.find_referencing_symbols("formatName", "lib/utils.ts")
        print(f"Found {len(references)} references to 'formatName':")
        for ref in references:
            symbol_name = ref.symbol.get_name_path()
            file_path = ref.symbol.location.relative_path
            line = ref.line
            char = ref.character
            print(f"  - {symbol_name} in {file_path} at line {line}, char {char}")
        
        # Test with User interface
        print("\n=== Testing find_referencing_symbols for User ===")
        references = retriever.find_referencing_symbols("User", "lib/store.ts")
        print(f"Found {len(references)} references to 'User':")
        for ref in references:
            symbol_name = ref.symbol.get_name_path()
            file_path = ref.symbol.location.relative_path
            line = ref.line
            char = ref.character
            print(f"  - {symbol_name} in {file_path} at line {line}, char {char}")
            
        # Test symbol finding to confirm it works
        print("\n=== Confirming symbol finding works ===")
        formatName_symbols = retriever.find_by_name("formatName", "lib/utils.ts")
        print(f"Found {len(formatName_symbols)} formatName symbols in lib/utils.ts")
        
        user_symbols = retriever.find_by_name("User", "lib/store.ts")
        print(f"Found {len(user_symbols)} User symbols in lib/store.ts")
            
    finally:
        ls.stop()
        print("\nLanguage server stopped")

if __name__ == "__main__":
    test_fixed_references()