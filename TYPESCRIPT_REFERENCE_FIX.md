# TypeScript Function Reference Issue - Root Cause and Solution

## Root Cause

After thorough investigation, the issue with `find_referencing_symbols` returning empty results for TypeScript functions in SvelteKit projects is:

1. **When using `language: typescript`**: The standalone TypeScript language server doesn't find references at all (returns 0 references even when they exist)

2. **When using `language: svelte`**: The Svelte adapter only finds references in .svelte files, not in .ts files

## Why This Happens

- The Svelte adapter's `_request_references_with_buffer` method correctly handles .svelte files by searching both Svelte and TypeScript language servers
- For .ts files, it delegates to `_find_references_in_svelte_files` which only searches within .svelte files
- The TypeScript language server within the Svelte adapter could find references, but the current implementation doesn't query it for .ts-to-.ts references

## The Fix

Modify the Svelte adapter's `_request_references_with_buffer` method to also query the TypeScript language server for references when dealing with .ts files:

```python
# In _request_references_with_buffer for .ts files:
if relative_file_path.endswith((".ts", ".js")) and self._wait_for_typescript_ready():
    all_results = []
    
    # Query TypeScript LS for references within TS files
    try:
        ts_results = self._ts_server.request_references(relative_file_path, line, column)
        all_results.extend(ts_results)
        self.logger.log(f"TypeScript LS found {len(ts_results)} references", logging.INFO)
    except Exception as e:
        self.logger.log(f"TypeScript LS references failed: {e}", logging.WARNING)
    
    # Also search Svelte files for references to this TypeScript symbol
    try:
        svelte_refs = self._find_references_in_svelte_files(relative_file_path, line, column)
        all_results.extend(svelte_refs)
        if svelte_refs:
            self.logger.log(f"Found {len(svelte_refs)} references in Svelte files", logging.INFO)
    except Exception as e:
        self.logger.log(f"Svelte file reference search failed: {e}", logging.WARNING)
    
    return self._deduplicate_locations(all_results)
```

This is already partially implemented but the TypeScript language server query is missing.

## Testing Confirmed

- With `language: svelte`, the adapter finds references in .svelte files correctly
- The bridged TypeScript server works properly when queried
- The issue is simply that .ts-to-.ts references aren't being searched

## Impact

This fix would enable proper cross-file reference tracking for:
- TypeScript utility functions used across multiple .ts files
- Interfaces and types referenced in both .ts and .svelte files
- Full bidirectional navigation in SvelteKit projects