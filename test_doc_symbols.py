import os
import sys
sys.path.insert(0, 'src')

from solidlsp.ls_config import Language, LanguageServerConfig
from solidlsp.ls import SolidLanguageServer
from solidlsp.ls_logger import LanguageServerLogger
import logging

# Setup
repo_path = os.path.abspath("test/resources/repos/svelte/test_repo")
config = LanguageServerConfig(Language.SVELTE)
logger = LanguageServerLogger(log_level=logging.INFO)

print(f"Creating Svelte language server for {repo_path}")
ls = SolidLanguageServer.create(config, logger, repo_path)

try:
    ls.start()
    print("Language server started\n")
    
    # Test document symbols
    print("=== Document symbols for lib/utils.ts ===")
    hierarchical, flat = ls.request_document_symbols("lib/utils.ts", include_body=False)
    
    print(f"\nHierarchical symbols: {len(hierarchical)}")
    for sym in hierarchical:
        print(f"  - {sym.get('name')} (kind={sym.get('kind')})")
        
    print(f"\nFlat symbols: {len(flat)}")
    for sym in flat[:10]:  # First 10
        print(f"  - {sym.get('name')} (kind={sym.get('kind')}) at {sym.get('location', {}).get('relativePath', '?')}")
        
finally:
    ls.stop()
    print("\nLanguage server stopped")