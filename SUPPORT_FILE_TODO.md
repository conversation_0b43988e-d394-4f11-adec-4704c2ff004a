# Support File Implementation TODO

## Phase 1: Core Infrastructure ✅
- [x] Add `get_support_files()` method to SolidLanguageServer
- [x] Add `is_support_file()` method to SolidLanguageServer
- [x] Update `Project.validate_relative_path()` with language_server and write parameters
- [x] Add `filter_support_files()` helper to Project class

## Phase 2: Update Editing Tools ✅
- [x] Update CreateTextFileTool to add write=True
- [x] Update ReplaceRegexTool to add write=True
- [x] Update DeleteLinesTool to add write=True
- [x] Update ReplaceLinesTool to add write=True
- [x] Update InsertAtLineTool to add write=True
- [x] Symbol editing tools use code_editor (no direct validation needed)

## Phase 3: Update File Enumeration Logic ✅
- [x] Update Project.gather_source_files() to filter support files
- [x] Update SolidLanguageServer.request_full_symbol_tree()
- [x] Update SolidLanguageServer.request_dir_overview()
- [x] Update SolidLanguageServer.request_references()
- [x] Update GetSymbolsOverviewTool
- [x] Update FindSymbolTool

## Phase 4: Svelte Language Server ✅
- [x] Override get_support_files() in SvelteProxyLanguageServer

## Phase 5: Testing ✅
- [x] Test read access to .svelte-kit files
- [x] Test write protection for .svelte-kit files
- [x] Test filtering in symbol overview
- [x] Test scope limitation (node_modules should still fail)