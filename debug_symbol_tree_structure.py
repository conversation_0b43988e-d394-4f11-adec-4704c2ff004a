#!/usr/bin/env python3

import sys
import os
import json
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from solidlsp.ls_config import LanguageServerConfig
from solidlsp.ls_logger import LanguageServerLogger
from solidlsp.language_servers.svelte_language_server import SvelteProxyLanguageServer
from serena.symbol import LanguageServerSymbolRetriever

def debug_symbol_tree():
    # Setup
    repo_path = "/Users/<USER>/MCP/serena/test/resources/repos/svelte/test_repo"
    config = LanguageServerConfig(code_language="svelte")
    logger = LanguageServerLogger()
    
    # Create language server
    ls = SvelteProxyLanguageServer.create(config, logger, repo_path)
    
    try:
        ls.start()
        print("Language server started successfully")
        
        # Test symbol tree for lib/utils.ts specifically
        print("\n=== Testing symbol tree for lib/utils.ts ===")
        symbol_tree = ls.request_full_symbol_tree("lib/utils.ts")
        
        def print_symbol_structure(symbols, indent=0):
            for sym in symbols:
                prefix = "  " * indent
                name = sym.get('name', 'unknown')
                kind = sym.get('kind', 'unknown')
                rel_path = sym.get('location', {}).get('relativePath', 'unknown')
                children_count = len(sym.get('children', []))
                print(f"{prefix}{name} (kind={kind}, path={rel_path}, children={children_count})")
                
                if sym.get('children'):
                    print_symbol_structure(sym['children'], indent + 1)
        
        print(f"Found {len(symbol_tree)} root symbols:")
        print_symbol_structure(symbol_tree)
        
        # Test the retriever's find_by_name method
        print("\n=== Testing LanguageServerSymbolRetriever ===")
        retriever = LanguageServerSymbolRetriever(ls)
        
        # Try to find formatName in lib/utils.ts
        symbols_in_file = retriever.find_by_name("formatName", "lib/utils.ts")
        print(f"Found {len(symbols_in_file)} symbols named 'formatName' in lib/utils.ts:")
        for sym in symbols_in_file:
            print(f"  - {sym.get_name_path()} (kind={sym.kind})")
        
        # Compare with document symbols
        print("\n=== Comparing with document symbols ===")
        _, doc_symbols = ls.request_document_symbols("lib/utils.ts")
        print(f"Document symbols found {len(doc_symbols)} root symbols:")
        for sym in doc_symbols:
            name = sym.get('name', 'unknown')
            kind = sym.get('kind', 'unknown')
            print(f"  - {name} (kind={kind})")
            
    finally:
        ls.stop()
        print("\nLanguage server stopped")

if __name__ == "__main__":
    debug_symbol_tree()