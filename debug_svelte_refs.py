#!/usr/bin/env python3
"""Debug script to test Svelte reference finding."""

import os
import sys
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from solidlsp.ls_config import Language, LanguageServerConfig
from solidlsp.ls import SolidLanguageServer
from solidlsp.ls_logger import LanguageServerLogger
import logging

def test_references():
    # Setup
    repo_path = "test/resources/repos/svelte/test_repo"
    config = LanguageServerConfig(Language.SVELTE)
    logger = LanguageServerLogger(log_level=logging.DEBUG)
    
    print(f"Creating Svelte language server for {repo_path}")
    ls = SolidLanguageServer.create(config, logger, repo_path)
    
    try:
        ls.start()
        print("Language server started")
        
        # Test 1: Find references to User interface in store.ts
        print("\n=== Test 1: References to User interface ===")
        refs = ls.request_references(os.path.join("lib", "store.ts"), 2, 18)
        print(f"Found {len(refs)} references:")
        for ref in refs:
            print(f"  - {ref['uri']}: Line {ref['range']['start']['line'] + 1}")
        
        # Test 2: Find references to formatName function in utils.ts
        print("\n=== Test 2: References to formatName function ===")
        refs = ls.request_references(os.path.join("lib", "utils.ts"), 2, 17)
        print(f"Found {len(refs)} references:")
        for ref in refs:
            print(f"  - {ref['uri']}: Line {ref['range']['start']['line'] + 1}")
            
    finally:
        ls.stop()
        print("\nLanguage server stopped")

if __name__ == "__main__":
    test_references()