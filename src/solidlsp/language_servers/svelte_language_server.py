"""
Svelte Proxy Language Server - The Brain

This implementation provides comprehensive cross-file intelligence for Svelte projects
by acting as a proxy that coordinates three child processes:
- The official Svelte Language Server for Svelte-specific features
- The official TypeScript Language Server for robust TypeScript analysis
- A minimal `svelte2tsx` bridge to create virtual TypeScript documents from
  Svelte components, enabling cross-file intelligence

Architecture:
    Serena MCP Core
         ↓ LSP
    SvelteProxyLanguageServer (Python - The Brain)
         ↓          ↓              ↓
    Svelte LS   TypeScript LS   svelte2tsx Bridge
    (stdio)     (stdio)         (stdio)
"""

import contextlib
import hashlib
import json
import logging
import os
import pathlib
import shutil
import subprocess
import threading
from collections import OrderedDict
from collections.abc import Iterator
from dataclasses import dataclass
from typing import Any

from overrides import override

from solidlsp import ls_types
from solidlsp.ls import LSPFileBuffer, ReferenceInSymbol, SolidLanguageServer
from solidlsp.ls_config import Language, LanguageServerConfig
from solidlsp.ls_logger import LanguageServerLogger
from solidlsp.ls_utils import PathUtils, PlatformId, PlatformUtils
from solidlsp.lsp_protocol_handler.lsp_types import InitializeParams
from solidlsp.lsp_protocol_handler.server import ProcessLaunchInfo

from .common import RuntimeDependency, RuntimeDependencyCollection

# Maximum number of virtual files to keep in memory for long-running sessions
MAX_VIRTUAL_FILES = 500


@dataclass
class TransformResult:
    """Result of Svelte to TSX transformation with source map."""

    tsx_content: str
    source_map: dict[str, Any] | None

    def has_source_map(self) -> bool:
        return self.source_map is not None


class SourceMapHelper:
    """
    In-house VLQ decoder + position translator.
    Handles the subset of the source-map spec Svelte2TSX emits
    (which is still full spec: generatedLine;generatedColumn;source;
     originalLine;originalColumn[;name]).
    """

    # ------------------------------------------------------------------ #
    # VLQ helpers
    _B64 = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"
    _B64_VAL = {ch: i for i, ch in enumerate(_B64)}

    @staticmethod
    def _vlq_numbers(segment: str) -> list[int]:
        """
        Decode a single base64-VLQ segment into a list of signed ints.
        Spec: https://sourcemaps.info/spec.html#Base64_VLQ_mappings
        """
        nums, value, shift = [], 0, 0
        for ch in segment:
            digit = SourceMapHelper._B64_VAL.get(ch, 0)
            cont = digit & 32  # continuation bit (b5)
            digit &= 31  # data bits (b0-4)
            value += digit << shift
            shift += 5
            if not cont:  # end of current number
                negative = value & 1
                value >>= 1
                nums.append(-value if negative else value)
                # reset for next integer in this segment
                value, shift = 0, 0
        return nums

    # ------------------------------------------------------------------ #

    def __init__(self, source_map_data: dict[str, Any] | None):
        self._g2o: dict[tuple[int, int], tuple[int, int]] = {}
        self._o2g: dict[tuple[int, int], tuple[int, int]] = {}
        self._by_line: dict[int, list[tuple[int, int, int]]] = {}  # generatedLine -> [(gCol, oLine, oCol)]

        if source_map_data and source_map_data.get("mappings"):
            self._parse(source_map_data["mappings"])

    # ------------------------------------------------------------------ #
    # Public API
    def translate_position_to_generated(self, original_line: int, original_column: int) -> tuple[int, int] | None:
        key = (original_line, original_column)
        hit = self._o2g.get(key)
        if hit:
            return hit
        # fallback: previous mapping on same original line
        # (rarely used but helps inside long expressions)
        return None

    def translate_position_to_original(self, generated_line: int, generated_column: int) -> tuple[int, int] | None:
        key = (generated_line, generated_column)
        hit = self._g2o.get(key)
        if hit:
            return hit

        # no exact hit - binary-search the closest segment on that generated line
        segs = self._by_line.get(generated_line)
        if not segs:
            return None
        lo, hi = 0, len(segs) - 1
        while lo <= hi:
            mid = (lo + hi) // 2
            if segs[mid][0] == generated_column:
                return (segs[mid][1], segs[mid][2])
            if segs[mid][0] < generated_column:
                lo = mid + 1
            else:
                hi = mid - 1
        # nearest previous
        if hi >= 0:
            _, o_l, o_c = segs[hi]
            return (o_l, o_c)
        return None

    # ------------------------------------------------------------------ #

    # ------------------------------------------------------------------ #
    # Internal parser
    def _parse(self, mappings: str) -> None:
        generated_line = 0
        # running deltas
        generated_col = original_line = original_col = src_idx = name_idx = 0

        for line in mappings.split(";"):
            if line == "":
                generated_line += 1
                generated_col = 0
                continue

            generated_col = 0
            segments = line.split(",")
            for seg in segments:
                nums = self._vlq_numbers(seg)
                if not nums:
                    continue

                generated_col += nums[0]  # generated column delta

                if len(nums) > 1:
                    src_idx += nums[1]  # source index - unused
                    original_line += nums[2]
                    original_col += nums[3]
                    if len(nums) > 4:
                        name_idx += nums[4]  # name index - unused

                    g_key = (generated_line, generated_col)
                    o_key = (original_line, original_col)

                    # store first hit for each key
                    self._g2o.setdefault(g_key, o_key)
                    self._o2g.setdefault(o_key, g_key)
                    self._by_line.setdefault(generated_line, []).append((generated_col, original_line, original_col))

            generated_line += 1

    # ------------------------------------------------------------------ #


class LRUCache:
    """Simple LRU cache implementation using OrderedDict."""

    def __init__(self, max_size: int):
        self.max_size = max_size
        self.cache: OrderedDict[str, str] = OrderedDict()

    def get(self, key: str) -> str | None:
        """Get value and mark as recently used."""
        if key in self.cache:
            # Move to end (most recently used)
            value = self.cache.pop(key)
            self.cache[key] = value
            return value
        return None

    def put(self, key: str, value: str) -> str | None:
        """Put value and evict LRU if necessary. Returns evicted key if any."""
        evicted_key = None

        if key in self.cache:
            # Update existing key
            self.cache.pop(key)
        elif len(self.cache) >= self.max_size:
            # Evict least recently used (first item)
            evicted_key, _ = self.cache.popitem(last=False)

        # Add as most recently used
        self.cache[key] = value
        return evicted_key

    def remove(self, key: str) -> bool:
        """Remove key if exists. Returns True if removed."""
        if key in self.cache:
            self.cache.pop(key)
            return True
        return False

    def __len__(self) -> int:
        return len(self.cache)

    def __contains__(self, key: str) -> bool:
        return key in self.cache


class SvelteProxyLanguageServer(SolidLanguageServer):
    """
    Svelte Proxy Language Server - The orchestrating brain.

    This adapter coordinates three child processes to provide comprehensive
    cross-file intelligence between .svelte and .ts files.
    """

    def __init__(self, config: LanguageServerConfig, logger: LanguageServerLogger, repository_root_path: str):
        """Initialize the proxy and its child language servers."""
        # The main process launch info is for the Svelte LS
        svelte_cmd = self._setup_runtime_dependencies(logger, config)
        super().__init__(
            config,
            logger,
            repository_root_path,
            ProcessLaunchInfo(cmd=svelte_cmd, cwd=repository_root_path),
            "svelte",
        )

        # TypeScript Language Server for cross-file intelligence (using VTS adapter)
        ts_config = LanguageServerConfig(Language.TYPESCRIPT_VTS)
        self._ts_server: SolidLanguageServer | None = None
        self._ts_server_error: Exception | None = None
        self._ts_ready = threading.Event()
        try:
            # Import and create VTS TypeScript LS for better performance and reliability
            from solidlsp.language_servers.vts_language_server import VtsLanguageServer

            self._ts_server = VtsLanguageServer(ts_config, logger, repository_root_path)
            self.logger.log("Created VTS TypeScript LS for cross-file intelligence", logging.INFO)
        except Exception as e:
            self.logger.log(f"VTS TypeScript LS creation failed: {e} (config: {ts_config})", logging.WARNING)

            self._ts_server_error = e

        # svelte2tsx bridge for transformation
        self._bridge_process: subprocess.Popen | None = None
        self._bridge_error: Exception | None = None
        self._bridge_request_id = 0
        self._bridge_responses: dict[int, dict] = {}
        self._bridge_events: dict[int, threading.Event] = {}
        self._bridge_lock = threading.Lock()
        self._content_cache: dict[str, TransformResult] = {}

        # Virtual file URI mappings
        self._svelte_to_tsx_uri_map = LRUCache(MAX_VIRTUAL_FILES)
        self._tsx_to_svelte_uri_map = LRUCache(MAX_VIRTUAL_FILES)
        self._uri_mapping_lock = threading.Lock()

        self._source_map_cache: dict[str, SourceMapHelper] = {}

        self._open_virtual_files: set[str] = set()
        self._virtual_files_lock = threading.Lock()

    def _start_bridge(self) -> None:
        """Starts the svelte2tsx Node.js bridge."""
        bridge_path = os.path.join(os.path.dirname(__file__), "svelte2tsx_bridge.js")
        try:
            node_path = shutil.which("node")
            if not node_path:
                self.logger.log("Node.js not found. Bridge disabled.", logging.WARNING)
                return

            # Set working directory to where node_modules are installed
            bridge_cwd = os.path.join(self.__class__.ls_resources_dir(), "svelte")

            # Set NODE_PATH to help Node.js find the modules
            env = os.environ.copy()
            node_modules_path = os.path.join(bridge_cwd, "node_modules")
            env["NODE_PATH"] = node_modules_path

            self._bridge_process = subprocess.Popen(
                [node_path, bridge_path],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=bridge_cwd,  # Run from the npm dependencies directory
                env=env,  # Include NODE_PATH for module resolution
            )

            def reader(pipe, log_prefix):
                for line in iter(pipe.readline, b""):
                    try:
                        decoded_line = line.decode().strip()
                        if log_prefix == "[Bridge STDERR]":
                            self.logger.log(f"{log_prefix} {decoded_line}", logging.WARNING)
                            continue

                        response = json.loads(decoded_line)
                        with self._bridge_lock:
                            req_id = response["id"]
                            self._bridge_responses[req_id] = response

                            if req_id in self._bridge_events:
                                self._bridge_events[req_id].set()
                    except (json.JSONDecodeError, KeyError):
                        self.logger.log(f"{log_prefix} Invalid response: {line.decode()}", logging.WARNING)

            # Start reader threads
            threading.Thread(target=reader, args=(self._bridge_process.stdout, "[Bridge STDOUT]"), daemon=True).start()
            threading.Thread(target=reader, args=(self._bridge_process.stderr, "[Bridge STDERR]"), daemon=True).start()

            self.logger.log("svelte2tsx bridge started", logging.INFO)

        except Exception as e:
            self.logger.log(f"Bridge startup failed: {e} (script: {bridge_path})", logging.WARNING)
            self._bridge_process = None
            self._bridge_error = e

    def _fail_pending_bridge_requests(self) -> None:
        """Fail all pending bridge requests with an error response to prevent hanging."""
        with self._bridge_lock:
            if self._bridge_events:
                self.logger.log(f"Failing {len(self._bridge_events)} pending bridge requests due to restart", logging.WARNING)

                # Create error responses for all pending requests and notify waiting threads
                for req_id in list(self._bridge_events.keys()):
                    self._bridge_responses[req_id] = {"id": req_id, "error": "Bridge restarted - request failed"}

                    self._bridge_events[req_id].set()

    def _add_uri_mapping(self, svelte_uri: str, tsx_uri: str, source_map_helper: SourceMapHelper | None = None) -> None:
        """Add bidirectional URI mapping with LRU eviction handling."""
        with self._uri_mapping_lock:
            # First check if we need to remove existing mappings for these URIs
            existing_tsx = self._svelte_to_tsx_uri_map.get(svelte_uri)
            if existing_tsx and existing_tsx != tsx_uri:
                self._tsx_to_svelte_uri_map.remove(existing_tsx)

            existing_svelte = self._tsx_to_svelte_uri_map.get(tsx_uri)
            if existing_svelte and existing_svelte != svelte_uri:
                self._svelte_to_tsx_uri_map.remove(existing_svelte)

            # Add svelte -> tsx mapping, handle eviction
            evicted_svelte = self._svelte_to_tsx_uri_map.put(svelte_uri, tsx_uri)
            if evicted_svelte:
                # Get the corresponding TSX URI for the evicted Svelte URI
                evicted_tsx_uri = self._tsx_to_svelte_uri_map.get(evicted_svelte)
                if evicted_tsx_uri:
                    self._tsx_to_svelte_uri_map.remove(evicted_tsx_uri)
                # Clean up source map cache for evicted entry
                self._source_map_cache.pop(evicted_svelte, None)
                self.logger.log(f"Evicted virtual file mapping: {evicted_svelte}", logging.DEBUG)

            # Add tsx -> svelte mapping, handle eviction
            evicted_tsx = self._tsx_to_svelte_uri_map.put(tsx_uri, svelte_uri)
            if evicted_tsx:
                # Clean up forward mapping for evicted entry
                self._svelte_to_tsx_uri_map.remove(evicted_tsx)

            # Store source map for position translation
            if source_map_helper:
                self._source_map_cache[svelte_uri] = source_map_helper

    def _get_tsx_uri(self, svelte_uri: str) -> str | None:
        """Get TSX URI for Svelte URI, marking as recently used."""
        with self._uri_mapping_lock:
            return self._svelte_to_tsx_uri_map.get(svelte_uri)

    def _get_svelte_uri(self, tsx_uri: str) -> str | None:
        """Get Svelte URI for TSX URI, marking as recently used."""
        with self._uri_mapping_lock:
            return self._tsx_to_svelte_uri_map.get(tsx_uri)

    def _remove_uri_mapping(self, svelte_uri: str) -> bool:
        """Remove bidirectional URI mapping."""
        with self._uri_mapping_lock:
            tsx_uri = self._svelte_to_tsx_uri_map.get(svelte_uri)
            if tsx_uri:
                self._svelte_to_tsx_uri_map.remove(svelte_uri)
                self._tsx_to_svelte_uri_map.remove(tsx_uri)
                # Clean up source map cache
                self._source_map_cache.pop(svelte_uri, None)
                return True
            return False

    def _get_source_map_helper(self, svelte_uri: str) -> SourceMapHelper | None:
        """Get source map helper for position translation."""
        return self._source_map_cache.get(svelte_uri)

    def _start_typescript_server_async(self) -> None:
        """Start TypeScript server in background thread to prevent blocking on npm restore."""

        def start_ts_worker():
            try:
                if self._ts_server:
                    self.logger.log("Starting TypeScript LS in background thread", logging.INFO)
                    self._ts_server.start()
                    self._ts_ready.set()
                    self.logger.log("TypeScript LS started and ready", logging.INFO)
            except Exception as e:
                self.logger.log(f"TypeScript LS startup failed: {e}", logging.WARNING)
                self._ts_server = None
                self._ts_server_error = e

                self._ts_ready.set()

        if self._ts_server:

            ts_thread = threading.Thread(target=start_ts_worker, daemon=True)
            ts_thread.start()
            self.logger.log("TypeScript LS startup initiated in background", logging.INFO)
        else:

            self._ts_ready.set()

    def _wait_for_typescript_ready(self, timeout: float = 30.0) -> bool:
        """Wait for TypeScript server to be ready and ensure proper indexing. Returns True if ready, False if timeout."""
        if not self._ts_server:
            self.logger.log("TypeScript server not available", logging.DEBUG)
            return False

        ready = self._ts_ready.wait(timeout)
        if not ready:
            self.logger.log(f"TypeScript LS not ready after {timeout}s timeout", logging.WARNING)
            return False
            
        # Ensure the server has had adequate time for cross-file indexing
        # This addresses the core issue where TS server needs time to build its reference graph
        if not hasattr(self, '_ts_server_indexed'):
            self.logger.log("Allowing TypeScript server additional time for cross-file indexing...", logging.DEBUG)
            import time
            time.sleep(0.3)  # Brief pause for indexing - less than the retry mechanism
            self._ts_server_indexed = True
            
        return ready and self._ts_server is not None

    def _ensure_typescript_available(self) -> None:
        """Ensure TypeScript server is available, raising error if not.

        Raises:
            RuntimeError: If TypeScript server is unavailable

        """
        if not self._wait_for_typescript_ready():
            if self._ts_server_error:
                raise self._ts_server_error
            raise RuntimeError(
                f"TypeScript language server not available (has_server={self._ts_server is not None}, ready={self._ts_ready.is_set()})"
            )

    def _restart_child(self, child_name: str) -> None:
        """Restart a child process if it crashes."""
        self.logger.log(f"Restarting {child_name} child process", logging.WARNING)
        if child_name == "bridge":
            # Fail any pending requests before restarting to prevent hanging
            self._fail_pending_bridge_requests()

            if self._bridge_process:
                try:
                    self._bridge_process.terminate()
                    self._bridge_process.wait(timeout=1.0)
                except (subprocess.TimeoutExpired, OSError) as e:
                    # Process may already be dead or unresponsive - force kill
                    self.logger.log(f"Bridge process termination failed: {e}", logging.DEBUG)
                    try:
                        self._bridge_process.kill()
                    except OSError:
                        pass  # Process already dead
            self._start_bridge()
        elif child_name == "typescript" and self._ts_server:
            try:
                self._ts_server.stop()
                self._ts_ready.clear()

                with self._virtual_files_lock:
                    self._open_virtual_files.clear()
                self._start_typescript_server_async()
                self.logger.log("TypeScript LS restart initiated", logging.INFO)
            except Exception as e:
                self.logger.log(f"Failed to restart TypeScript LS: {e}", logging.ERROR)

    def _transform_svelte_to_tsx(self, uri: str, content: str) -> TransformResult:
        """Transform Svelte code to TSX using the bridge, with caching.

        Raises:
            RuntimeError: If bridge is unavailable or transformation fails

        """
        if not self._bridge_process:
            if self._bridge_error:
                raise self._bridge_error
            raise RuntimeError(f"Bridge process not available for transformation: {uri}")

        # Check if bridge process is still alive
        if self._bridge_process.poll() is not None:
            self.logger.log("Bridge process died, restarting", logging.WARNING)
            self._restart_child("bridge")
            raise RuntimeError(f"Bridge process died unexpectedly for {uri} (restart attempted)")

        # Content hashing for debounce
        content_hash = hashlib.md5(content.encode()).hexdigest()
        cache_key = f"{uri}:{content_hash}"

        if cache_key in self._content_cache:
            return self._content_cache[cache_key]

        try:
            # Create event for this request and send request
            with self._bridge_lock:
                req_id = self._bridge_request_id
                self._bridge_request_id += 1

                self._bridge_events[req_id] = threading.Event()

            request = json.dumps({"id": req_id, "filename": PathUtils.uri_to_path(uri), "code": content})

            self._bridge_process.stdin.write((request + "\n").encode())
            self._bridge_process.stdin.flush()

            event = self._bridge_events[req_id]
            if event.wait(timeout=5.0):  # 5 second timeout
                # Response received, process it
                with self._bridge_lock:
                    response = self._bridge_responses.pop(req_id, None)
                    self._bridge_events.pop(req_id, None)

                if response:
                    if "error" in response:
                        raise RuntimeError(f"Bridge transformation error for {uri}: {response['error']}")

                    result_data = response.get("result")
                    if result_data:
                        transform_result = TransformResult(tsx_content=result_data.get("tsx", ""), source_map=result_data.get("map"))
                        if transform_result.tsx_content:
                            self._content_cache[cache_key] = transform_result
                        return transform_result

                    tsx_content = response.get("tsx")
                    if tsx_content:
                        transform_result = TransformResult(tsx_content=tsx_content, source_map=None)
                        self._content_cache[cache_key] = transform_result
                        return transform_result
            else:

                with self._bridge_lock:
                    self._bridge_responses.pop(req_id, None)
                    self._bridge_events.pop(req_id, None)
                raise TimeoutError(f"Bridge transformation timed out after 5 seconds for {uri} (request_id={req_id})")

        except (BrokenPipeError, OSError) as e:
            self._restart_child("bridge")
            raise RuntimeError(f"Bridge communication failed for {uri}: {e} (restart attempted)") from e
        except Exception as e:
            raise RuntimeError(f"Bridge transformation failed for {uri}: {e}") from e

    @classmethod
    def _setup_runtime_dependencies(cls, logger: LanguageServerLogger, config: LanguageServerConfig) -> str:
        """Set up Svelte LS, TypeScript LS, and svelte2tsx dependencies."""
        platform_id = PlatformUtils.get_platform_id()
        valid_platforms = [
            PlatformId.LINUX_x64,
            PlatformId.LINUX_arm64,
            PlatformId.OSX_x64,
            PlatformId.OSX_arm64,
            PlatformId.WIN_x64,
        ]
        assert platform_id in valid_platforms, f"Platform {platform_id} not supported for Svelte"

        # System prerequisites
        assert shutil.which("node") is not None, "Node.js not installed or not in PATH"
        assert shutil.which("npm") is not None, "npm not installed or not in PATH"

        deps = RuntimeDependencyCollection(
            [
                RuntimeDependency(
                    id="svelteserver",
                    description="Svelte Language Server",
                    command="npm install --prefix ./ svelte-language-server@latest",
                    platform_id="any",
                ),
                RuntimeDependency(
                    id="svelte2tsx",
                    description="Svelte to TypeScript transformation tool",
                    command="npm install --prefix ./ svelte2tsx@latest svelte@latest",
                    platform_id="any",
                ),
            ]
        )

        # Installation
        ls_dir = os.path.join(cls.ls_resources_dir(), "svelte")
        executable_path = os.path.join(ls_dir, "node_modules", ".bin", "svelteserver")

        if not os.path.exists(executable_path):
            deps.install(logger, ls_dir)

        assert os.path.exists(executable_path), f"Svelte LS not found at {executable_path}"
        return f"{executable_path} --stdio"

    @staticmethod
    def _get_initialize_params(repository_absolute_path: str) -> InitializeParams:
        """Initialize params for Svelte Language Server."""
        root_uri = pathlib.Path(repository_absolute_path).as_uri()
        return {
            "locale": "en",
            "capabilities": {
                "textDocument": {
                    "synchronization": {"didSave": True, "dynamicRegistration": True},
                    "completion": {"dynamicRegistration": True, "completionItem": {"snippetSupport": True}},
                    "definition": {"dynamicRegistration": True},
                    "references": {"dynamicRegistration": True},
                    "documentSymbol": {
                        "dynamicRegistration": True,
                        "hierarchicalDocumentSymbolSupport": True,
                        "symbolKind": {"valueSet": list(range(1, 27))},
                    },
                    "hover": {"dynamicRegistration": True, "contentFormat": ["markdown", "plaintext"]},
                    "signatureHelp": {"dynamicRegistration": True},
                    "codeAction": {"dynamicRegistration": True},
                },
                "workspace": {
                    "workspaceFolders": True,
                    "didChangeConfiguration": {"dynamicRegistration": True},
                    "symbol": {"dynamicRegistration": True},
                },
            },
            "processId": os.getpid(),
            "rootPath": repository_absolute_path,
            "rootUri": root_uri,
            "workspaceFolders": [{"uri": root_uri, "name": os.path.basename(repository_absolute_path)}],
        }

    @override
    def _start_server(self) -> None:
        """Start the Svelte language server and set up communication."""

        def register_capability_handler(params):
            pass

        def window_log_message(msg):
            self.logger.log(f"Svelte LS: {msg['message']}", logging.INFO)

        def do_nothing(params):
            pass

        # Set up handlers
        self.server.on_request("client/registerCapability", register_capability_handler)
        self.server.on_notification("window/logMessage", window_log_message)
        self.server.on_notification("$/progress", do_nothing)
        self.server.on_notification("textDocument/publishDiagnostics", do_nothing)

        # Start server
        self.logger.log("Starting Svelte language server", logging.INFO)
        self.server.start()

        # Initialize
        initialize_params = self._get_initialize_params(self.repository_root_path)
        init_response = self.server.send.initialize(initialize_params)
        assert "textDocumentSync" in init_response["capabilities"]

        self.server.notify.initialized({})
        self.completions_available.set()
        self.logger.log("Svelte language server ready", logging.INFO)

    @override
    def start(self) -> "SolidLanguageServer":
        """Start all child processes: Svelte LS, TypeScript LS, and bridge."""
        super().start()

        # Start TypeScript LS in background thread to prevent blocking
        self._start_typescript_server_async()

        # Start bridge
        self._start_bridge()

        self.logger.log("Svelte proxy and all child processes started", logging.INFO)
        return self

    @override
    def stop(self, shutdown_timeout: float = 2.0) -> None:
        """Stop all child processes gracefully."""
        # Stop TypeScript LS
        if self._ts_server:
            try:
                self._ts_server.stop(shutdown_timeout)
                self.logger.log("TypeScript LS stopped", logging.INFO)
            except Exception as e:
                self.logger.log(f"Error stopping TypeScript LS: {e}", logging.WARNING)

        # Stop bridge
        if self._bridge_process:
            # Fail any pending requests before stopping
            self._fail_pending_bridge_requests()

            try:
                # Send exit command first for graceful shutdown
                try:
                    exit_cmd = json.dumps({"jsonrpc": "2.0", "method": "exit"}) + "\n"
                    self._bridge_process.stdin.write(exit_cmd.encode())
                    self._bridge_process.stdin.flush()
                except (BrokenPipeError, OSError) as e:
                    # Bridge process may have already exited
                    self.logger.log(f"Bridge exit command failed: {e}", logging.DEBUG)

                # Wait briefly then terminate
                self._bridge_process.terminate()
                self._bridge_process.wait(timeout=0.5)
                self.logger.log("Bridge stopped", logging.INFO)
            except Exception as e:
                self.logger.log(f"Error stopping bridge: {e}", logging.WARNING)

        # Stop main Svelte LS
        super().stop(shutdown_timeout)
        self.logger.log("All child processes stopped", logging.INFO)

    @contextlib.contextmanager
    @override
    def open_file(self, relative_file_path: str) -> Iterator[LSPFileBuffer]:
        """
        Virtual File System Management - The core of cross-file intelligence.

        For .svelte files:
        1. Open in Svelte LS
        2. Transform to TSX via bridge
        3. Open virtual .svelte.tsx file in TypeScript LS
        4. Maintain bidirectional URI mapping
        """
        is_svelte = relative_file_path.endswith(".svelte")

        with super().open_file(relative_file_path) as svelte_buffer:
            if is_svelte and self._bridge_process and self._wait_for_typescript_ready():
                # Transform Svelte to TSX
                try:
                    transform_result = self._transform_svelte_to_tsx(svelte_buffer.uri, svelte_buffer.contents)
                except Exception as e:
                    self.logger.log(f"Svelte transformation failed for {relative_file_path}: {e}", logging.WARNING)
                    transform_result = None
                if transform_result and transform_result.tsx_content and self._ts_server:
                    tsx_uri = f"{svelte_buffer.uri}.tsx"

                    # Create source map helper for position translation
                    source_map_helper = SourceMapHelper(transform_result.source_map) if transform_result.has_source_map() else None

                    # Update bidirectional mapping with LRU eviction and source map
                    self._add_uri_mapping(svelte_buffer.uri, tsx_uri, source_map_helper)

                    # Open virtual document in TypeScript LS (only if not already open)
                    with self._virtual_files_lock:
                        if tsx_uri not in self._open_virtual_files:
                            try:
                                self._ts_server.server.notify.did_open_text_document(
                                    {
                                        "textDocument": {
                                            "uri": tsx_uri,
                                            "languageId": "typescriptreact",
                                            "version": svelte_buffer.version,
                                            "text": transform_result.tsx_content,
                                        }
                                    }
                                )
                                self._open_virtual_files.add(tsx_uri)
                                self.logger.log(f"Opened virtual TSX file: {tsx_uri}", logging.DEBUG)
                            except Exception as e:
                                self.logger.log(f"Failed to open virtual TSX: {e}", logging.WARNING)
                        else:
                            # File already open, just update content
                            try:
                                self._ts_server.server.notify.did_change_text_document(
                                    {
                                        "textDocument": {"uri": tsx_uri, "version": svelte_buffer.version + 1},
                                        "contentChanges": [{"text": transform_result.tsx_content}],
                                    }
                                )
                                self.logger.log(f"Updated virtual TSX file: {tsx_uri}", logging.DEBUG)
                            except Exception as e:
                                self.logger.log(f"Failed to update virtual TSX: {e}", logging.WARNING)

            yield svelte_buffer

            # Cleanup: close virtual document
            tsx_uri = self._get_tsx_uri(svelte_buffer.uri) if is_svelte else None
            if tsx_uri:
                with self._virtual_files_lock:
                    if tsx_uri in self._open_virtual_files:
                        try:
                            if self._ts_server and self._ts_ready.is_set():
                                self._ts_server.server.notify.did_close_text_document({"textDocument": {"uri": tsx_uri}})
                                self.logger.log(f"Closed virtual TSX file: {tsx_uri}", logging.DEBUG)
                            self._open_virtual_files.remove(tsx_uri)
                        except Exception as e:
                            self.logger.log(f"Failed to close virtual TSX: {e}", logging.WARNING)

                # Clean up mappings
                self._remove_uri_mapping(svelte_buffer.uri)

    def _query_typescript_via_virtual_file(self, tsx_uri: str, line: int, column: int, method: str = "definition") -> list:
        """Query TypeScript LS via virtual TSX file. Used for both definition and references."""
        try:
            # Send request to TypeScript LS (no position translation - use original coordinates)
            if method == "definition":
                params = {
                    "textDocument": {"uri": tsx_uri},
                    "position": {"line": line, "character": column},
                }
                response = self._ts_server.server.send.definition(params)
            else:  # references
                params = {
                    "textDocument": {"uri": tsx_uri},
                    "position": {"line": line, "character": column},
                    "context": {"includeDeclaration": True},
                }
                response = self._ts_server.server.send.references(params)

            # Convert response to internal format
            if response:
                results = self._convert_lsp_locations_to_internal(response)
                self.logger.log(f"TypeScript LS found {len(results)} {method} results", logging.INFO)
                return results
            return []
        except Exception as e:
            # Don't raise error here - this is a helper method, let callers handle gracefully
            self.logger.log(f"TypeScript LS {method} via virtual file failed: {e}", logging.WARNING)
            return []

    def _request_definition_with_buffer(
        self, relative_file_path: str, line: int, column: int, buffer: LSPFileBuffer | None = None
    ) -> list[ls_types.Location]:
        """
        Internal definition finding that can reuse an existing buffer to avoid recursion.

        Args:
            relative_file_path: Path to the file
            line: Line number (0-based)
            column: Column number (0-based)
            buffer: Optional existing buffer to reuse (prevents open_file recursion)

        """
        # For standalone .ts/.js files, use TypeScript LS primarily but also check Svelte LS
        if relative_file_path.endswith((".ts", ".js")) and self._wait_for_typescript_ready():
            all_results = []

            # Primary: TypeScript LS
            try:
                ts_results = self._ts_server.request_definition(relative_file_path, line, column)
                all_results.extend(ts_results)
                self.logger.log(f"TypeScript LS found {len(ts_results)} definitions", logging.INFO)
            except Exception as e:
                self.logger.log(f"TypeScript LS definition failed: {e}", logging.WARNING)

            # Note: For cross-definitions from TS to Svelte, we rely on the TypeScript LS
            # which should be aware of Svelte components through the virtual TSX files

            return self._deduplicate_locations(all_results)

        # For .svelte files, use multiplexing
        if relative_file_path.endswith(".svelte"):
            svelte_results = []
            ts_results = []

            # Query Svelte LS
            try:
                svelte_results = super().request_definition(relative_file_path, line, column)
                self.logger.log(f"Svelte LS found {len(svelte_results)} definitions", logging.INFO)
            except Exception as e:
                self.logger.log(f"Svelte LS definition failed: {e}", logging.WARNING)

            # Query TypeScript LS via virtual file
            if self._wait_for_typescript_ready():
                if buffer:
                    tsx_uri = self._get_tsx_uri(buffer.uri)
                    if tsx_uri:
                        ts_results = self._query_typescript_via_virtual_file(tsx_uri, line, column, "definition")

                    # If virtual file didn't work, try import analysis approach
                    if not ts_results:
                        self.logger.log("Virtual file approach failed, trying import-based approach", logging.INFO)
                        ts_results = self._try_import_based_definition(buffer.contents, line, column, relative_file_path)
                else:
                    # Fallback: open file if no buffer provided
                    with self.open_file(relative_file_path) as file_buffer:
                        tsx_uri = self._get_tsx_uri(file_buffer.uri)
                        if tsx_uri:
                            ts_results = self._query_typescript_via_virtual_file(tsx_uri, line, column, "definition")

                        # If virtual file didn't work, try import analysis approach
                        if not ts_results:
                            self.logger.log("Virtual file approach failed, trying import-based approach", logging.INFO)
                            ts_results = self._try_import_based_definition(file_buffer.contents, line, column, relative_file_path)

            # Merge results: prefer .ts/.tsx definitions over .svelte import statements
            merged = self._merge_definition_results(svelte_results, ts_results)
            return merged

        # Fallback to Svelte LS
        return super().request_definition(relative_file_path, line, column)

    @override
    def request_definition(self, relative_file_path: str, line: int, column: int) -> list[ls_types.Location]:
        """
        Cross-file definition finding - Phase 4 multiplexing implementation.

        Strategy: Query both servers and merge results, prioritizing TypeScript LS
        for cross-file intelligence.
        """
        return self._request_definition_with_buffer(relative_file_path, line, column, None)

    @override
    def request_document_symbols(
        self, relative_file_path: str, include_body: bool = False
    ) -> tuple[list[ls_types.UnifiedSymbolInformation], list[ls_types.UnifiedSymbolInformation]]:
        """
        Request document symbols with proper TypeScript file handling.
        
        For .ts/.js files, delegate to the TypeScript LS.
        For .svelte files, use the Svelte LS.
        """
        # For TypeScript/JavaScript files, use the TypeScript LS
        if relative_file_path.endswith((".ts", ".js", ".tsx", ".jsx")) and self._wait_for_typescript_ready():
            try:
                return self._ts_server.request_document_symbols(relative_file_path, include_body)
            except Exception as e:
                self.logger.log(f"TypeScript LS document symbols failed: {e}", logging.WARNING)
        
        # For everything else (including .svelte), use the Svelte LS
        return super().request_document_symbols(relative_file_path, include_body)

    def _merge_definition_results(self, svelte_results: list, ts_results: list) -> list:
        """Merge definition results, preferring TypeScript LS for .ts/.tsx files."""
        # If TypeScript LS found definitions, prefer those
        if ts_results:
            # Filter for actual .ts/.tsx files (not import statements)
            ts_files = [r for r in ts_results if r.get("uri", "").endswith((".ts", ".tsx"))]
            if ts_files:
                return ts_files

        # Otherwise return Svelte results
        return svelte_results

    def _request_references_with_buffer(
        self, relative_file_path: str, line: int, column: int, buffer: LSPFileBuffer | None = None
    ) -> list[ls_types.Location]:
        """
        Internal reference finding that can reuse an existing buffer to avoid recursion.

        Args:
            relative_file_path: Path to the file
            line: Line number (0-based)
            column: Column number (0-based)
            buffer: Optional existing buffer to reuse (prevents open_file recursion)

        """
        # For TypeScript files, search both TS files and Svelte files that might reference it
        if relative_file_path.endswith((".ts", ".js")) and self._wait_for_typescript_ready():
            all_results = []
            self.logger.log(f"Starting comprehensive reference search for TypeScript file: {relative_file_path}", logging.INFO)

            # Query TypeScript LS for references within TS files
            try:
                self.logger.log("Querying TypeScript language server for references...", logging.DEBUG)
                ts_results = self._ts_server.request_references(relative_file_path, line, column)
                all_results.extend(ts_results)
                self.logger.log(f"TypeScript LS found {len(ts_results)} references in .ts/.js files", logging.INFO)
                
                # Log the found references for debugging
                for i, ref in enumerate(ts_results[:5]):  # Log first 5 references
                    self.logger.log(f"  TS ref {i+1}: {ref.uri} at line {ref.range.start.line}", logging.DEBUG)
                    
            except Exception as e:
                self.logger.log(f"TypeScript LS references failed: {e}", logging.WARNING)
                # Don't give up - continue with Svelte file search

            # Also search Svelte files for references to this TypeScript symbol
            # Note: Svelte LS doesn't support reference finding for pure TypeScript files,
            # so we use our custom search method
            try:
                self.logger.log("Searching Svelte files for references to TypeScript symbol...", logging.DEBUG)
                svelte_refs = self._find_references_in_svelte_files(relative_file_path, line, column)
                all_results.extend(svelte_refs)
                if svelte_refs:
                    self.logger.log(f"Manual search found {len(svelte_refs)} references in Svelte files", logging.INFO)
                    for i, ref in enumerate(svelte_refs[:3]):  # Log first 3 references
                        self.logger.log(f"  Svelte ref {i+1}: {ref.uri} at line {ref.range.start.line}", logging.DEBUG)
                else:
                    self.logger.log("No references found in Svelte files", logging.DEBUG)
            except Exception as e:
                self.logger.log(f"Manual Svelte file reference search failed: {e}", logging.WARNING)

            deduplicated_results = self._deduplicate_locations(all_results)
            self.logger.log(f"Total references found: {len(deduplicated_results)} (after deduplication)", logging.INFO)
            return deduplicated_results

        # For .svelte files, multiplex both servers
        if relative_file_path.endswith(".svelte"):
            all_results = []

            # Query Svelte LS
            try:
                svelte_results = super().request_references(relative_file_path, line, column)
                all_results.extend(svelte_results)
            except Exception as e:
                self.logger.log(f"Svelte LS references failed: {e}", logging.WARNING)

            # Query TypeScript LS via virtual file
            if self._wait_for_typescript_ready():
                if buffer:
                    tsx_uri = self._get_tsx_uri(buffer.uri)
                    if tsx_uri:
                        ts_results = self._query_typescript_via_virtual_file(tsx_uri, line, column, "references")
                        all_results.extend(ts_results)
                else:
                    # Fallback: open file if no buffer provided
                    with self.open_file(relative_file_path) as file_buffer:
                        tsx_uri = self._get_tsx_uri(file_buffer.uri)
                        if tsx_uri:
                            ts_results = self._query_typescript_via_virtual_file(tsx_uri, line, column, "references")
                            all_results.extend(ts_results)

            # Remove duplicates
            return self._deduplicate_locations(all_results)

        return super().request_references(relative_file_path, line, column)

    @override
    def request_references(self, relative_file_path: str, line: int, column: int) -> list[ls_types.Location]:
        """Cross-file reference finding with multiplexing."""
        return self._request_references_with_buffer(relative_file_path, line, column, None)

    def _try_import_based_definition(self, svelte_content: str, line: int, column: int, relative_file_path: str) -> list:
        """
        Fallback approach: analyze imports in the Svelte file and directly query the imported files.
        This works around position mapping issues between Svelte and TSX transformations.
        """
        self.logger.log("Starting import-based definition search", logging.DEBUG)
        try:
            # Get the symbol at the cursor position
            lines = svelte_content.split("\n")
            if line >= len(lines):
                return []

            target_line = lines[line]
            if column >= len(target_line):
                return []

            # Simple heuristic: find the word at the cursor position
            import re

            # Find word boundaries around the cursor position
            start_col = column
            end_col = column

            # If we're not on an alphanumeric character, try to find the next one
            if not (target_line[column].isalnum() or target_line[column] == "_"):
                # Look forward for the next word
                while end_col < len(target_line) and not (target_line[end_col].isalnum() or target_line[end_col] == "_"):
                    end_col += 1
                start_col = end_col

            # Go backwards to find word start
            while start_col > 0 and (target_line[start_col - 1].isalnum() or target_line[start_col - 1] == "_"):
                start_col -= 1

            # Go forwards to find word end
            while end_col < len(target_line) and (target_line[end_col].isalnum() or target_line[end_col] == "_"):
                end_col += 1

            if start_col == end_col:
                return []

            symbol_name = target_line[start_col:end_col]
            self.logger.log(f"Looking for symbol '{symbol_name}' in imports", logging.INFO)

            # Find imports in the script section
            script_match = re.search(r"<script[^>]*>(.*?)</script>", svelte_content, re.DOTALL)
            if not script_match:
                return []

            script_content = script_match.group(1)

            # Look for imports of this symbol (including TypeScript type imports)
            import_patterns = [
                # Type imports
                rf'import\s*type\s*\{{\s*[^}}]*{re.escape(symbol_name)}[^}}]*\}}\s*from\s*[\'"]([^\'"]+)[\'"]',
                rf'import\s*type\s*\{{\s*{re.escape(symbol_name)}\s*\}}\s*from\s*[\'"]([^\'"]+)[\'"]',
                # Regular imports
                rf'import\s*\{{\s*[^}}]*{re.escape(symbol_name)}[^}}]*\}}\s*from\s*[\'"]([^\'"]+)[\'"]',
                rf'import\s*\{{\s*{re.escape(symbol_name)}\s*\}}\s*from\s*[\'"]([^\'"]+)[\'"]',
                rf'import\s*{re.escape(symbol_name)}\s*from\s*[\'"]([^\'"]+)[\'"]',
            ]

            for pattern in import_patterns:
                match = re.search(pattern, script_content)
                if match:
                    import_path = match.group(1)
                    self.logger.log(f"Found import of '{symbol_name}' from '{import_path}'", logging.INFO)

                    # Resolve the import path relative to the current file
                    import os

                    current_dir = os.path.dirname(relative_file_path)

                    # Try different extensions
                    possible_extensions = [".ts", ".tsx", ".js", ".jsx"]
                    for ext in possible_extensions:
                        target_file = os.path.join(current_dir, import_path + ext)
                        target_file = os.path.normpath(target_file)

                        try:
                            # Query the TypeScript LS for this symbol in the target file
                            definitions = self._find_symbol_in_ts_file(target_file, symbol_name)
                            if definitions:
                                self.logger.log(f"Found {len(definitions)} definitions in {target_file}", logging.INFO)
                                return definitions
                        except Exception as e:
                            self.logger.log(f"Failed to query {target_file}: {e}", logging.WARNING)
                            continue

            return []

        except Exception as e:
            self.logger.log(f"Import-based definition search failed: {e}", logging.WARNING)
            return []

    def _find_references_in_svelte_files(self, ts_file_path: str, line: int, column: int) -> list:
        """
        Find references to a TypeScript symbol in Svelte files.

        This implements reverse references: when querying a TypeScript file for references,
        also search all Svelte files that might import and use symbols from that TypeScript file.
        """
        try:
            # First, get the symbol at the specified position in the TypeScript file
            symbol_name = self._get_symbol_at_position(ts_file_path, line, column)
            if not symbol_name:
                self.logger.log(f"No symbol found at {ts_file_path}:{line}:{column}", logging.WARNING)
                return []

            self.logger.log(f"Searching for TypeScript symbol '{symbol_name}' in Svelte files", logging.INFO)

            # Find all Svelte files in the project
            svelte_files = self._find_all_svelte_files()
            self.logger.log(f"Found {len(svelte_files)} Svelte files for reverse reference search", logging.DEBUG)
            references = []

            for svelte_file in svelte_files:
                try:
                    # Check if this Svelte file imports from the TypeScript file
                    file_refs = self._find_symbol_references_in_svelte_file(svelte_file, ts_file_path, symbol_name)
                    if file_refs:
                        self.logger.log(f"Found {len(file_refs)} references in {svelte_file}", logging.INFO)
                    references.extend(file_refs)
                except Exception as e:
                    self.logger.log(f"Error searching {svelte_file}: {e}", logging.WARNING)

            return references

        except Exception as e:
            self.logger.log(f"Error in reverse reference search: {e}", logging.WARNING)
            return []

    def _get_symbol_at_position(self, ts_file_path: str, line: int, column: int) -> str | None:
        """Get the symbol name at a specific position in a TypeScript file."""
        import re

        try:
            # Read the file content
            full_path = os.path.join(self.repository_root_path, ts_file_path)
            with open(full_path, encoding="utf-8") as f:
                content = f.read()

            lines = content.split("\n")
            if line >= len(lines):
                return None

            target_line = lines[line]

            # If column is out of bounds, try to find a meaningful symbol
            if column >= len(target_line):
                # This might be pointing to a member of an interface/class
                # Look backwards for the containing declaration
                for i in range(line - 1, -1, -1):
                    if i < len(lines):
                        prev_line = lines[i]
                        # Look for interface or class declarations
                        import re

                        interface_match = re.search(r"export\s+interface\s+([a-zA-Z_][a-zA-Z0-9_]*)", prev_line)
                        if interface_match:
                            symbol = interface_match.group(1)
                            return symbol

                        class_match = re.search(r"export\s+class\s+([a-zA-Z_][a-zA-Z0-9_]*)", prev_line)
                        if class_match:
                            symbol = class_match.group(1)
                            return symbol

                        # Stop if we hit another declaration or empty line
                        if prev_line.strip() == "" or prev_line.startswith("export"):
                            break

                # Fallback: look for any identifier on the current line
                match = re.search(r"\b[a-zA-Z_][a-zA-Z0-9_]*\b", target_line)
                if match:
                    symbol = match.group()
                    return symbol
                return None

            # Check if this line contains a function/class/interface declaration
            # and extract the symbol name directly
            declaration_patterns = [
                r"export\s+function\s+([a-zA-Z_][a-zA-Z0-9_]*)",
                r"export\s+class\s+([a-zA-Z_][a-zA-Z0-9_]*)",
                r"export\s+interface\s+([a-zA-Z_][a-zA-Z0-9_]*)",
                r"export\s+type\s+([a-zA-Z_][a-zA-Z0-9_]*)",
                r"export\s+enum\s+([a-zA-Z_][a-zA-Z0-9_]*)",
                r"export\s+const\s+([a-zA-Z_][a-zA-Z0-9_]*)",
                r"export\s+let\s+([a-zA-Z_][a-zA-Z0-9_]*)",
                r"export\s+var\s+([a-zA-Z_][a-zA-Z0-9_]*)",
                r"function\s+([a-zA-Z_][a-zA-Z0-9_]*)",
                r"class\s+([a-zA-Z_][a-zA-Z0-9_]*)",
                r"interface\s+([a-zA-Z_][a-zA-Z0-9_]*)",
            ]

            for pattern in declaration_patterns:
                match = re.search(pattern, target_line)
                if match:
                    symbol = match.group(1)
                    self.logger.log(f"Found declaration symbol '{symbol}' in line", logging.DEBUG)
                    return symbol

            # Extract symbol name at the position
            start_col = column
            end_col = column

            # If we're not on a letter/number, try to find the nearest one
            if not (target_line[column].isalnum() or target_line[column] == "_"):
                # Look forward for the next word
                while end_col < len(target_line) and not (target_line[end_col].isalnum() or target_line[end_col] == "_"):
                    end_col += 1
                start_col = end_col

                if start_col >= len(target_line):
                    return None

            # Find word boundaries
            while start_col > 0 and (target_line[start_col - 1].isalnum() or target_line[start_col - 1] == "_"):
                start_col -= 1

            while end_col < len(target_line) and (target_line[end_col].isalnum() or target_line[end_col] == "_"):
                end_col += 1

            if start_col == end_col:
                return None

            symbol = target_line[start_col:end_col]
            self.logger.log(f"Extracted symbol '{symbol}' from position {start_col}-{end_col}", logging.DEBUG)

            # If we extracted a keyword, look for the actual symbol name after it
            if symbol in ["export", "function", "class", "interface", "const", "let", "var", "type", "enum"]:
                # Look for the actual symbol name after the keyword
                remaining = target_line[end_col:].strip()
                match = re.match(r"^[\s*]*([a-zA-Z_][a-zA-Z0-9_]*)", remaining)
                if match:
                    actual_symbol = match.group(1)
                    self.logger.log(f"Found actual symbol '{actual_symbol}' after keyword '{symbol}'", logging.DEBUG)
                    return actual_symbol

            return symbol

        except Exception as e:
            self.logger.log(f"Error getting symbol at position: {e}", logging.WARNING)
            return None

    def _find_all_svelte_files(self) -> list[str]:
        """Find all .svelte files in the project."""
        svelte_files = []
        try:
            for root, dirs, files in os.walk(self.repository_root_path):
                # Skip ignored directories
                dirs[:] = [d for d in dirs if not self.is_ignored_dirname(d)]

                for file in files:
                    if file.endswith(".svelte"):
                        # Get relative path
                        full_path = os.path.join(root, file)
                        rel_path = os.path.relpath(full_path, self.repository_root_path)
                        svelte_files.append(rel_path)

        except Exception as e:
            self.logger.log(f"Error finding Svelte files: {e}", logging.WARNING)

        return svelte_files

    def _find_symbol_references_in_svelte_file(self, svelte_file: str, ts_file_path: str, symbol_name: str) -> list:
        """Find references to a TypeScript symbol within a specific Svelte file."""
        try:
            # Read the Svelte file
            full_path = os.path.join(self.repository_root_path, svelte_file)
            with open(full_path, encoding="utf-8") as f:
                content = f.read()

            # Check if this Svelte file imports from the TypeScript file
            imports_symbol = self._svelte_imports_from_ts_file(content, svelte_file, ts_file_path, symbol_name)
            self.logger.log(f"{svelte_file} imports '{symbol_name}' from {ts_file_path}: {imports_symbol}", logging.DEBUG)
            if not imports_symbol:
                return []

            # Find all occurrences of the symbol in the Svelte content
            references = []
            lines = content.split("\n")

            for line_idx, line in enumerate(lines):
                col_idx = 0
                while True:
                    col_idx = line.find(symbol_name, col_idx)
                    if col_idx == -1:
                        break

                    # Check if this is a whole word (not part of another identifier)
                    if self._is_whole_word(line, col_idx, len(symbol_name)):
                        # Create a reference location
                        svelte_uri = pathlib.Path(full_path).as_uri()
                        references.append(
                            {
                                "uri": svelte_uri,
                                "range": {
                                    "start": {"line": line_idx, "character": col_idx},
                                    "end": {"line": line_idx, "character": col_idx + len(symbol_name)},
                                },
                                "relativePath": svelte_file,
                                "absolutePath": full_path,
                            }
                        )

                    col_idx += 1

            return references

        except Exception as e:
            self.logger.log(f"Error finding references in {svelte_file}: {e}", logging.WARNING)
            return []

    def _svelte_imports_from_ts_file(self, svelte_content: str, svelte_file: str, ts_file_path: str, symbol_name: str) -> bool:
        """Check if a Svelte file imports the specified symbol from the TypeScript file."""
        try:
            import re

            # Extract script section
            script_match = re.search(r"<script[^>]*>(.*?)</script>", svelte_content, re.DOTALL)
            if not script_match:
                return False

            script_content = script_match.group(1)
            self.logger.log(f"Script content for {svelte_file}: {script_content[:200]}...", logging.DEBUG)

            # Calculate the relative import path from Svelte file to TypeScript file
            svelte_dir = os.path.dirname(svelte_file)
            ts_file_no_ext = os.path.splitext(ts_file_path)[0]  # Remove .ts extension

            # Calculate relative path
            if svelte_dir:
                relative_path = os.path.relpath(ts_file_no_ext, svelte_dir)
            else:
                relative_path = ts_file_no_ext

            # Normalize path separators and handle relative paths
            if not relative_path.startswith("."):
                relative_path = "./" + relative_path
            relative_path = relative_path.replace(os.sep, "/")

            self.logger.log(f"Expected import path for {svelte_file} -> {ts_file_path}: '{relative_path}'", logging.DEBUG)

            # Also check for alias-based imports (e.g., $lib/server/auth)
            # Extract just the filename part for comparison
            ts_filename = os.path.basename(ts_file_no_ext)

            # Check if the Svelte file imports this symbol from the TypeScript file
            import_patterns = [
                # Named imports: import { Symbol, other } from "./path"
                rf'import\s*\{{\s*[^}}]*{re.escape(symbol_name)}[^}}]*\}}\s*from\s*[\'"]([^\'"]+)[\'"]',
                # Simple named import: import { Symbol } from "./path"
                rf'import\s*\{{\s*{re.escape(symbol_name)}\s*\}}\s*from\s*[\'"]([^\'"]+)[\'"]',
                # Default import: import Symbol from "./path"
                rf'import\s*{re.escape(symbol_name)}\s*from\s*[\'"]([^\'"]+)[\'"]',
                # Type import: import type { Symbol } from "./path"
                rf'import\s+type\s*\{{\s*[^}}]*{re.escape(symbol_name)}[^}}]*\}}\s*from\s*[\'"]([^\'"]+)[\'"]',
                # Simple type import: import type { Symbol } from "./path"
                rf'import\s+type\s*\{{\s*{re.escape(symbol_name)}\s*\}}\s*from\s*[\'"]([^\'"]+)[\'"]',
                # Mixed import with type: import { other, type Symbol } from "./path"
                rf'import\s*\{{\s*[^}}]*type\s+{re.escape(symbol_name)}[^}}]*\}}\s*from\s*[\'"]([^\'"]+)[\'"]',
            ]

            for pattern in import_patterns:
                matches = re.finditer(pattern, script_content)
                for match in matches:
                    import_path = match.group(1)
                    self.logger.log(f"Found import: '{import_path}', checking against '{relative_path}'", logging.DEBUG)
                    # Normalize the import path
                    if import_path in (relative_path, ts_file_no_ext) or import_path + ".ts" == ts_file_path:
                        return True
                    # Check if this is an alias import (e.g., $lib/server/auth)
                    if import_path.endswith((f"/{ts_filename}", f"/{ts_filename}.ts")):
                        return True

            return False

        except Exception as e:
            self.logger.log(f"Error checking imports: {e}", logging.WARNING)
            return False

    def _is_whole_word(self, line: str, start_pos: int, word_len: int) -> bool:
        """Check if a word occurrence is a whole word (not part of another identifier)."""
        # Check character before
        if start_pos > 0:
            prev_char = line[start_pos - 1]
            if prev_char.isalnum() or prev_char == "_":
                return False

        # Check character after
        end_pos = start_pos + word_len
        if end_pos < len(line):
            next_char = line[end_pos]
            if next_char.isalnum() or next_char == "_":
                return False

        return True

    def _find_symbol_in_ts_file(self, target_file: str, symbol_name: str) -> list:
        """Find a symbol definition in a TypeScript file."""
        if not self._wait_for_typescript_ready():
            return []

        try:
            # Use the TypeScript LS to find the symbol
            symbols = self._ts_server.request_full_symbol_tree(target_file, include_body=False)

            # Search for the symbol in the tree
            def find_symbol_recursive(symbol_list, name):
                results = []
                for symbol in symbol_list:
                    if symbol.get("name") == name:
                        # Found the symbol, return its location
                        location = symbol.get("location", {})
                        if "uri" in location and "range" in location:
                            results.append({"uri": location["uri"], "range": location["range"]})

                    # Recursively search children
                    children = symbol.get("children", [])
                    results.extend(find_symbol_recursive(children, name))

                return results

            return find_symbol_recursive(symbols, symbol_name)

        except Exception as e:
            self.logger.log(f"Symbol search in {target_file} failed: {e}", logging.WARNING)
            return []

    def _convert_lsp_locations_to_internal(self, lsp_response) -> list:
        """Convert LSP definition response to internal Location format, mapping virtual TSX files back to Svelte."""
        results = []

        if isinstance(lsp_response, list):
            for item in lsp_response:
                if isinstance(item, dict) and "uri" in item and "range" in item:
                    # Standard Location format
                    uri = item["uri"]

                    # Map virtual TSX URIs back to Svelte files
                    original_uri = self._get_svelte_uri(uri) if uri.endswith(".svelte.tsx") else uri

                    absolute_path = PathUtils.uri_to_path(original_uri)
                    relative_path = PathUtils.get_relative_path(absolute_path, self.repository_root_path)
                    results.append(
                        {
                            "uri": original_uri,
                            "range": item["range"],
                            "absolutePath": absolute_path,
                            "relativePath": relative_path,
                        }
                    )
                elif isinstance(item, dict) and "targetUri" in item and "targetRange" in item:
                    # LocationLink format
                    uri = item["targetUri"]

                    # Map virtual TSX URIs back to Svelte files
                    original_uri = self._get_svelte_uri(uri) if uri.endswith(".svelte.tsx") else uri

                    absolute_path = PathUtils.uri_to_path(original_uri)
                    relative_path = PathUtils.get_relative_path(absolute_path, self.repository_root_path)
                    results.append(
                        {
                            "uri": original_uri,
                            "range": item["targetRange"],
                            "absolutePath": absolute_path,
                            "relativePath": relative_path,
                        }
                    )
        elif isinstance(lsp_response, dict):
            # Single location
            if "uri" in lsp_response and "range" in lsp_response:
                uri = lsp_response["uri"]

                # Map virtual TSX URIs back to Svelte files
                original_uri = self._get_svelte_uri(uri) if uri.endswith(".svelte.tsx") else uri

                absolute_path = PathUtils.uri_to_path(original_uri)
                relative_path = PathUtils.get_relative_path(absolute_path, self.repository_root_path)
                results.append(
                    {
                        "uri": original_uri,
                        "range": lsp_response["range"],
                        "absolutePath": absolute_path,
                        "relativePath": relative_path,
                    }
                )
            elif "targetUri" in lsp_response and "targetRange" in lsp_response:
                uri = lsp_response["targetUri"]

                # Map virtual TSX URIs back to Svelte files
                original_uri = self._get_svelte_uri(uri) if uri.endswith(".svelte.tsx") else uri

                absolute_path = PathUtils.uri_to_path(original_uri)
                relative_path = PathUtils.get_relative_path(absolute_path, self.repository_root_path)
                results.append(
                    {
                        "uri": original_uri,
                        "range": lsp_response["targetRange"],
                        "absolutePath": absolute_path,
                        "relativePath": relative_path,
                    }
                )

        return results

    def _deduplicate_locations(self, locations: list) -> list:
        """Remove duplicate locations based on URI and range."""
        seen = set()
        unique = []

        for loc in locations:
            key = (loc.get("uri"), str(loc.get("range")))
            if key not in seen:
                seen.add(key)
                unique.append(loc)

        return unique

    def _add_svelte_component_symbols(self, symbols: list[ls_types.UnifiedSymbolInformation]) -> list[ls_types.UnifiedSymbolInformation]:
        """Add synthetic component symbols for .svelte files."""
        import pathlib
        
        # Start with the original symbols
        enhanced_symbols = symbols.copy() if symbols else []
        
        # Find all .svelte files in the repository and add component symbols
        try:
            for root, dirs, files in os.walk(self.repository_root_path):
                for file in files:
                    if file.endswith('.svelte'):
                        full_path = os.path.join(root, file)
                        rel_path = os.path.relpath(full_path, self.repository_root_path)
                        
                        # Extract component name from file path
                        component_name = os.path.basename(rel_path).replace(".svelte", "")
                        
                        # Create a synthetic component symbol
                        component_symbol = {
                            "name": component_name,
                            "kind": 5,  # Class kind - represents a component
                            "location": {
                                "uri": pathlib.Path(full_path).as_uri(),
                                "range": {
                                    "start": {"line": 0, "character": 0},
                                    "end": {"line": 0, "character": len(component_name)}
                                },
                                "absolutePath": full_path,
                                "relativePath": rel_path
                            },
                            "parent": None,
                            "children": []
                        }
                        
                        enhanced_symbols.append(component_symbol)
                        
        except Exception as e:
            self.logger.log(f"Error adding Svelte component symbols: {e}", logging.WARNING)
        
        return enhanced_symbols

    @override
    def request_full_symbol_tree(
        self, within_relative_path: str | None = None, include_body: bool = False
    ) -> list[ls_types.UnifiedSymbolInformation]:
        """Merge symbol trees from both Svelte and TypeScript language servers."""
        # If requesting a specific file, delegate to the appropriate server
        if within_relative_path and not within_relative_path.endswith('/'):
            # Check if it's a file path
            import os
            full_path = os.path.join(self.repository_root_path, within_relative_path)
            if os.path.isfile(full_path):
                # For specific files, use the appropriate language server directly
                if within_relative_path.endswith('.svelte'):
                    return super().request_full_symbol_tree(within_relative_path, include_body)
                elif within_relative_path.endswith(('.ts', '.tsx', '.js', '.jsx')):
                    if self._wait_for_typescript_ready():
                        return self._ts_server.request_full_symbol_tree(within_relative_path, include_body)
                    else:
                        return []
                else:
                    # Fallback to base implementation
                    return super().request_full_symbol_tree(within_relative_path, include_body)
        
        # For directory-level requests, merge the symbol trees
        svelte_symbols = super().request_full_symbol_tree(within_relative_path, include_body)

        if self._wait_for_typescript_ready():
            try:
                ts_symbols = self._ts_server.request_full_symbol_tree(within_relative_path, include_body)

                # If we only have one source of symbols, return it directly
                if not svelte_symbols:
                    enhanced_ts = self._add_svelte_component_symbols(ts_symbols)
                    return enhanced_ts
                if not ts_symbols:
                    enhanced_svelte = self._add_svelte_component_symbols(svelte_symbols)
                    return enhanced_svelte

                # Both have symbols - need to merge hierarchically
                merged_symbols = []

                # Helper function to merge children from two symbol trees
                def merge_children(svelte_children, ts_children):
                    """Merge children from both trees, combining by relativePath."""
                    # Create a map of relativePath -> symbol for svelte children
                    merged_map = {}
                    for child in svelte_children:
                        rel_path = child["location"]["relativePath"]
                        merged_map[rel_path] = child

                    # Add/merge TS children
                    for ts_child in ts_children:
                        rel_path = ts_child["location"]["relativePath"]

                        # Skip virtual .svelte.tsx files
                        if rel_path.endswith(".svelte.tsx"):
                            continue

                        if rel_path in merged_map:
                            # Both have this path - merge their children if they're directories
                            existing = merged_map[rel_path]
                            if existing.get("kind") == 4 and ts_child.get("kind") == 4:  # Both packages
                                # Merge children recursively
                                existing_children = existing.get("children", [])
                                ts_children_list = ts_child.get("children", [])
                                merged_children = merge_children(existing_children, ts_children_list)
                                existing["children"] = merged_children
                                # Update parent references
                                for child in merged_children:
                                    child["parent"] = existing
                            # For files, prefer the existing one (Svelte takes priority)
                        else:
                            # Only TS has this - add it
                            merged_map[rel_path] = ts_child

                    return list(merged_map.values())

                # Merge at the root level
                merged_symbols = merge_children(svelte_symbols, ts_symbols)
                
                # Add synthetic component symbols to the merged result
                enhanced_merged = self._add_svelte_component_symbols(merged_symbols)
                return enhanced_merged

            except Exception as e:
                self.logger.log(f"TypeScript LS symbol tree failed: {e}", logging.WARNING)

        # Add synthetic component symbols to Svelte-only symbols
        enhanced_svelte = self._add_svelte_component_symbols(svelte_symbols)
        return enhanced_svelte

    @override
    def request_referencing_symbols(
        self,
        relative_file_path: str,
        line: int,
        column: int,
        include_imports: bool = True,
        include_self: bool = False,
        include_body: bool = False,
        include_file_symbols: bool = False,
    ) -> list[ReferenceInSymbol]:
        """
        Override to handle Svelte component name resolution and delegate TypeScript files to TS server.
        A component's primary symbol is its 'default' export.
        """
        # Pragmatic patch: If the query is on a .svelte file, it's highly likely
        # we are interested in the component's default export. We find its location
        # and query from there.
        if relative_file_path.endswith(".svelte"):
            try:
                content = self.retrieve_full_file_content(relative_file_path)
                lines = content.splitlines()
                for i, text_line in enumerate(lines):
                    if "export default" in text_line:
                        new_column = text_line.find("default")
                        if new_column != -1:
                            # Query using the position of the 'default' export.
                            return super().request_referencing_symbols(
                                relative_file_path, i, new_column, include_imports, include_self, include_body, include_file_symbols
                            )
            except Exception as e:
                self.logger.log(f"Heuristic for finding 'export default' failed in {relative_file_path}: {e}", logging.WARNING)

        # For TypeScript files, use our internal reference finding that properly handles both TS and Svelte files
        if (relative_file_path.endswith((".ts", ".tsx", ".js", ".jsx")) and 
            self._wait_for_typescript_ready()):
            try:
                # Use our internal method that properly queries the TypeScript server
                locations = self._request_references_with_buffer(relative_file_path, line, column)
                
                # Convert locations to ReferenceInSymbol format
                references = []
                for location in locations:
                    # For each reference location, we need to find the containing symbol
                    ref_path = location["relativePath"]
                    ref_line = location["range"]["start"]["line"]
                    ref_col = location["range"]["start"]["character"]
                    
                    # Get the containing symbol for this reference
                    containing_symbol = self.request_containing_symbol(ref_path, ref_line, ref_col, include_body=include_body)
                    if containing_symbol:
                        ref_symbol = ReferenceInSymbol(
                            symbol=containing_symbol,
                            line=ref_line,
                            character=ref_col
                        )
                        references.append(ref_symbol)
                
                return references
            except Exception as e:
                self.logger.log(f"TypeScript reference query failed for {relative_file_path}: {e}", logging.WARNING)

        # Fallback to the original behavior for all other cases.
        return super().request_referencing_symbols(
            relative_file_path, line, column, include_imports, include_self, include_body, include_file_symbols
        )

    @override
    def is_ignored_dirname(self, dirname: str) -> bool:
        """Extended ignore list for Svelte projects with SvelteKit type support."""
        # This check MUST come before the super() call.
        if dirname == ".svelte-kit":
            return False

        return super().is_ignored_dirname(dirname) or dirname in [
            "build",
            "dist",
            "coverage",
            ".vercel",
            ".netlify",
            ".next",
            "out",
        ]

    @override
    def is_ignored_path(self, relative_path: str, ignore_unsupported_files: bool = True) -> bool:
        """Override to handle both .svelte and TypeScript files in this proxy server."""
        # Quick file extension check first (no filesystem calls)
        if ignore_unsupported_files:
            svelte_matcher = Language.SVELTE.get_source_fn_matcher()
            ts_matcher = Language.TYPESCRIPT.get_source_fn_matcher()

            # Use filename-based matching to avoid filesystem calls
            if not svelte_matcher.is_relevant_filename(relative_path) and not ts_matcher.is_relevant_filename(relative_path):
                return True

        # Delegate other ignore logic to parent
        return super().is_ignored_path(relative_path, ignore_unsupported_files=False)

    @override
    def get_support_files(self) -> list[str]:
        """Declares SvelteKit's generated type files as essential support files."""
        return [
            ".svelte-kit/**/*.d.ts",
            ".svelte-kit/ambient.d.ts",
            ".svelte-kit/tsconfig.json",
        ]


# Export alias for compatibility with the LanguageServer factory
SvelteLanguageServer = SvelteProxyLanguageServer
