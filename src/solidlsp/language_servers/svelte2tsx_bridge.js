/**
 * Svelte2TSX Bridge - Phase 1 Implementation
 * 
 * A minimal, stateless Node.js script that transforms Svelte code to TSX
 * using the svelte2tsx library. Designed to be simple, fast, and maintainable.
 * 
 * Protocol:
 * - Input: JSON lines on stdin with {id, filename, code}
 * - Output: JSON lines on stdout with {id, tsx} or {id, error}
 * - Caching: Results keyed by (hash(code), compilerVersion)
 */

const readline = require('readline');
const crypto = require('crypto');

// Cache for transformation results with size tracking
const transformCache = new Map();
const MAX_CACHE_ENTRIES = 500; // Reduced for multi-instance scenarios
const MAX_INPUT_SIZE = 2 * 1024 * 1024; // 2MB limit per file
let cacheMemoryUsage = 0;

// Lazy-load dependencies to support ESM modules
let svelte2tsx = null;
let svelte = null;

async function loadDependencies() {
    if (!svelte2tsx) {
        try {
            // Modern Node.js approach - avoid path manipulation
            const { createRequire } = require('module');
            const require_from_script = createRequire(__filename);
            
            // Try ESM import first (newer versions)
            try {
                svelte2tsx = await import('svelte2tsx');
                const svelteModule = await import('svelte/compiler');
                svelte = svelteModule;
            } catch (esmError) {
                // Fallback to CommonJS require  
                try {
                    svelte2tsx = require_from_script('svelte2tsx');
                    svelte = require_from_script('svelte/compiler');
                } catch (requireError) {
                    throw new Error(`Failed to load dependencies. ESM: ${esmError.message}, CJS: ${requireError.message}`);
                }
            }
        } catch (e) {
            throw new Error(`Module resolution failed: ${e.message}`);
        }
    }
}

async function transformSvelte(filename, code) {
    await loadDependencies();
    
    // Input size validation for multi-instance safety
    if (code.length > MAX_INPUT_SIZE) {
        throw new Error(`Input too large: ${code.length} bytes (max: ${MAX_INPUT_SIZE})`);
    }
    
    // Generate cache key from content hash and compiler version
    const contentHash = crypto.createHash('md5').update(code).digest('hex');
    const cacheKey = `${contentHash}:${svelte.VERSION}`;
    
    // Check cache first
    if (transformCache.has(cacheKey)) {
        return transformCache.get(cacheKey);
    }
    
    try {
        const result = svelte2tsx.svelte2tsx(code, {
            filename: filename,
            isTsFile: true,
            mode: 'ts',
            parse: svelte.parse,
            version: svelte.VERSION
        });
        
        // Cache the successful result with source map
        const transformResult = {
            tsx: result.code,
            map: result.map ? {
                // Serialize source map data for transmission
                version: result.map.version,
                sources: result.map.sources,
                names: result.map.names,
                mappings: result.map.mappings,
                sourcesContent: result.map.sourcesContent
            } : null
        };
        
        // Memory-aware cache management
        const resultSize = JSON.stringify(transformResult).length;
        cacheMemoryUsage += resultSize;
        transformCache.set(cacheKey, transformResult);
        
        // Aggressive eviction for multi-instance scenarios
        while (transformCache.size > MAX_CACHE_ENTRIES || cacheMemoryUsage > 50 * 1024 * 1024) { // 50MB max
            const firstKey = transformCache.keys().next().value;
            const evicted = transformCache.get(firstKey);
            if (evicted) {
                cacheMemoryUsage -= JSON.stringify(evicted).length;
            }
            transformCache.delete(firstKey);
        }
        
        return transformResult;
    } catch (error) {
        // Log more context about what failed to transform
        const preview = code.substring(0, 200).replace(/\n/g, '\\n');
        throw new Error(`Transformation failed: ${error.message} (file: ${filename}, preview: ${preview})`);
    }
}

// Set up readline interface
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
    terminal: false,
});

// Process incoming requests
rl.on('line', async (line) => {
    try {
        const request = JSON.parse(line);
        const { id, filename, code } = request;
        
        // Enhanced input validation
        if (typeof id === 'undefined') {
            process.stdout.write(JSON.stringify({ 
                id: 'unknown', 
                error: 'Missing request id' 
            }) + '\n');
            return;
        }
        
        if (typeof filename !== 'string' || typeof code !== 'string') {
            process.stdout.write(JSON.stringify({ 
                id, 
                error: 'Invalid request format. Expected {id, filename: string, code: string}' 
            }) + '\n');
            return;
        }
        
        // Additional validation for multi-instance safety
        if (code.length === 0) {
            process.stdout.write(JSON.stringify({ 
                id, 
                error: 'Empty code input' 
            }) + '\n');
            return;
        }
        
        try {
            const transformResult = await transformSvelte(filename, code);
            process.stdout.write(JSON.stringify({ id, result: transformResult }) + '\n');
        } catch (error) {
            process.stdout.write(JSON.stringify({ id, error: error.message }) + '\n');
        }
        
    } catch (parseError) {
        // Ignore invalid JSON lines (non-request data)
        // This is expected behavior for robustness
    }
});

// Handle process termination gracefully
process.on('SIGTERM', () => {
    rl.close();
    process.exit(0);
});

process.on('SIGINT', () => {
    rl.close();
    process.exit(0);
});

// Handle exit command for graceful shutdown
rl.on('line', (line) => {
    try {
        const parsed = JSON.parse(line);
        if (parsed.method === 'exit') {
            rl.close();
            process.exit(0);
        }
    } catch (e) {
        // Not a JSON-RPC exit command, ignore
    }
});