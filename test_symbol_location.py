import os
import sys
sys.path.insert(0, 'src')

from solidlsp.ls_config import Language, LanguageServerConfig
from solidlsp.ls import SolidLanguageServer
from solidlsp.ls_logger import LanguageServerLogger
from serena.symbol import LanguageServerSymbolRetriever
import logging

# Setup
repo_path = os.path.abspath("test/resources/repos/svelte/test_repo")
config = LanguageServerConfig(Language.SVELTE)
logger = LanguageServerLogger(log_level=logging.INFO)

print(f"Creating Svelte language server for {repo_path}")
ls = SolidLanguageServer.create(config, logger, repo_path)

try:
    ls.start()
    print("Language server started\n")
    
    # Create symbol retriever
    retriever = LanguageServerSymbolRetriever(ls)
    
    # First check what symbols are in the file
    print("=== Document symbols in utils.ts ===")
    doc_symbols = ls.request_document_symbols(os.path.join("lib", "utils.ts"))
    print(f"Found {len(doc_symbols[0])} top-level symbols, {len(doc_symbols[1])} flat symbols")
    
    # Try full symbol tree
    print("\n=== Full symbol tree ===")
    full_tree = ls.request_full_symbol_tree()
    
    # Find utils.ts in the tree
    def find_utils(symbols, path=""):
        for sym in symbols:
            if "utils.ts" in sym.get("location", {}).get("relativePath", ""):
                print(f"Found utils.ts node: {sym.get('name')}")
                children = sym.get("children", [])
                if children:
                    print(f"  Has {len(children)} children:")
                    for child in children:
                        print(f"    - {child.get('name')} (kind={child.get('kind')})")
            if sym.get("children"):
                find_utils(sym["children"], path + "/" + sym.get("name", "?"))
    
    find_utils(full_tree)
    
    # Find formatName symbol
    print("\n=== Finding formatName symbol ===")
    # Try without path restriction first
    all_symbols = retriever.find_by_name("formatName")
    print(f"Found {len(all_symbols)} symbols named formatName globally")
    if all_symbols:
        print(f"  Path: {all_symbols[0].location.relative_path}")
    
    path1 = os.path.join("lib", "utils.ts")
    path2 = "lib/utils.ts"
    print(f"  os.path.join result: {repr(path1)}")
    print(f"  Direct path: {repr(path2)}")
    
    symbols = retriever.find_by_name("formatName", within_relative_path="lib/utils.ts")
    print(f"Found {len(symbols)} symbols in utils.ts with direct path")
    
    # Also try with substring matching
    substr_symbols = retriever.find_by_name("formatName", substring_matching=True, within_relative_path=os.path.join("lib", "utils.ts"))
    print(f"Found {len(substr_symbols)} symbols with substring matching")
    if symbols:
        sym = symbols[0]
        print(f"Found {sym.name} at:")
        print(f"  File: {sym.location.relative_path}")
        print(f"  Line: {sym.location.line}")
        print(f"  Column: {sym.location.column}")
        print(f"  End Line: {sym.location.end_line}")
        print(f"  End Column: {sym.location.end_column}")
        
        # Now try to find references
        print("\n=== Finding references using retriever ===")
        refs = retriever.find_referencing_symbols_by_location(sym.location)
        print(f"Found {len(refs)} references")
        
        # Also try direct LS call
        print("\n=== Direct LS call ===")
        direct_refs = ls.request_references(sym.location.relative_path, sym.location.line, sym.location.column)
        print(f"Direct LS found {len(direct_refs)} references")
        
finally:
    ls.stop()
    print("\nLanguage server stopped")