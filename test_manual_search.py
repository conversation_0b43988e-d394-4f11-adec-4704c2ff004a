import os
import sys
sys.path.insert(0, 'src')

from solidlsp.ls_config import Language, LanguageServerConfig
from solidlsp.ls import SolidLanguageServer
from solidlsp.ls_logger import LanguageServerLogger
import logging

# Setup
repo_path = os.path.abspath("test/resources/repos/svelte/test_repo")
config = LanguageServerConfig(Language.SVELTE)
logger = LanguageServerLogger(log_level=logging.INFO)

print(f"Creating Svelte language server for {repo_path}")
ls = SolidLanguageServer.create(config, logger, repo_path)

try:
    ls.start()
    print("Language server started\n")
    
    # Test manual search method directly
    print("=== Testing manual search method ===")
    refs = ls._find_references_in_svelte_files(os.path.join("lib", "store.ts"), 2, 18)
    print(f"Manual search found {len(refs)} references:")
    for ref in refs:
        uri = ref['uri']
        line = ref['range']['start']['line'] + 1
        print(f"  - {uri}: Line {line}")
    
finally:
    ls.stop()
    print("\nLanguage server stopped")