#!/usr/bin/env python3
"""Simple test to debug TypeScript reference finding."""

import logging
from pathlib import Path

from src.solidlsp.language_servers.svelte_language_server import SvelteProxyLanguageServer
from src.solidlsp.ls_config import LanguageServerConfig, Language

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(message)s')

# Test paths
repo_path = Path("test/resources/repos/svelte/test_repo").absolute()

# Create Svelte language server
config = LanguageServerConfig(code_language=Language.SVELTE)
logger = logging.getLogger("test_svelte")
ls = SvelteProxyLanguageServer(config, logger, str(repo_path))

try:
    ls.start()
    
    # Wait for TypeScript server
    if ls._wait_for_typescript_ready():
        print("✅ TypeScript server is ready")
        
        # Test finding references to formatName function
        refs = ls.request_references("lib/simple.ts", 0, 16)  # formatName position
        print(f"\n🔍 Found {len(refs)} references to formatName:")
        
        for ref in refs:
            file_path = ref['uri'].replace(f"file://{repo_path}/", "")
            line = ref['range']['start']['line'] + 1
            print(f"   - {file_path}:{line}")
        
        # Also test the TypeScript server directly
        if ls._ts_server:
            print("\n🔍 Direct TypeScript server query:")
            ts_refs = ls._ts_server.request_references("lib/simple.ts", 0, 16)
            print(f"   Found {len(ts_refs)} references")
            for ref in ts_refs:
                file_path = ref['uri'].replace(f"file://{repo_path}/", "")
                line = ref['range']['start']['line'] + 1
                print(f"   - {file_path}:{line}")
    else:
        print("❌ TypeScript server failed to start")
        
finally:
    ls.stop()