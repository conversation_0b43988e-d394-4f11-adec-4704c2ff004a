### **Implement the "Support File" Architecture (Production-Ready Spec)**

**Objective:**

Implement the "Support File" architecture in Serena. The goal is to allow language server adapters to safely access framework-generated files that are normally in `.gitignore`, while ensuring these files remain invisible and strictly read-only to user-facing tools.

**Background & Context:**

Modern web frameworks generate essential type definitions into build directories (e.g., `.svelte-kit/types/`) that are correctly gitignored. For a language server to provide full cross-file intelligence, it must read these files. The solution is a generic "Support File" concept that allows adapters to declare the specific, non-user-code files they need for analysis.

**The Architectural Plan:**

Implement a "Support File" tier. These are files that:
1.  The language server is **allowed to read**, even if they are in `.gitignore`.
2.  Are **invisible** to user-facing tools.
3.  Are strictly **read-only**.

**Implementation Steps:**

**1. Modify the `SolidLanguageServer` Base Class**
*   **File:** `src/solidlsp/ls.py`
*   **Action:** Add two new methods to the `SolidLanguageServer` class.

```python
# Add these methods inside the SolidLanguageServer class

def get_support_files(self) -> list[str]:
    """
    Returns a list of glob patterns (using fnmatch syntax) for files that are
    essential for the language server's analysis but are not user-editable
    source code. Paths must be project-relative strings.

    These files will be accessible for reading but will be strictly read-only
    and will be hidden from user-facing tools like symbol searches.
    """
    return []

def is_support_file(self, relative_path: str) -> bool:
    """
    Checks if a given path matches the glob patterns for support files.
    The path passed must be a project-relative string.
    """
    # Coerce to string and guard against directory traversal
    relative_path = str(relative_path)
    if relative_path.startswith(("../", "..\\")):
        return False
        
    patterns = self.get_support_files()
    if not patterns:
        return False
    
    from fnmatch import fnmatch
    return any(fnmatch(relative_path, pattern) for pattern in patterns)
```

**2. Centralize Access and Read-Only Logic in `Project`**
*   **File:** `src/serena/project.py`
*   **Action:** Update `validate_relative_path`. Ensure the `is_path_in_project` check happens first.

```python
# In Project.validate_relative_path

def validate_relative_path(
    self,
    relative_path: str,
    language_server: "SolidLanguageServer | None" = None,
    write: bool = False,
) -> None:
    """
    Validates a path is safe to access. The `write` flag defaults to False;
    existing read-only callers require no change.
    
    - Checks that the path is within the project root.
    - Checks if the path is ignored by gitignore patterns.
    - Allows a language_server to override the ignore check for essential "support files".
    - Enforces that support files are strictly read-only.
    """
    if not self.is_path_in_project(relative_path):
        raise ValueError(f"{relative_path=} points to path outside of the repository root; cannot access for safety reasons")

    if self.is_ignored_path(relative_path):
        # Check for the support file override
        if language_server and language_server.is_support_file(relative_path):
            if write:
                raise ValueError(f"Cannot edit '{relative_path}' because it is a read-only framework support file.")
            # If it's a read operation, allow access.
            return
        
        raise ValueError(f"Path {relative_path} is ignored; cannot access for safety reasons")
```

**3. Update All Editing Tools to Signal Intent**
*   **Files:** `src/serena/tools/file_tools.py`, `src/serena/tools/symbolic_editing_tools.py`, etc.
*   **Action:** Find every call to `self.project.validate_relative_path` within any **editing tool** and add the `write=True` argument. Read-only tools require no changes.

```python
# Example for an editing tool's .apply method
language_server = self.agent.language_server if hasattr(self.agent, "language_server") else None
self.project.validate_relative_path(relative_path, language_server, write=True)
# ... (rest of the editing logic) ...
```

**4. Create a Centralized Filtering Helper**
*   **File:** `src/serena/project.py`
*   **Action:** Add this helper method to the `Project` class.

```python
# Add this method to the Project class in src/serena/project.py

def filter_support_files(
    self,
    paths: list[str],
    language_server: "SolidLanguageServer | None",
    include_support: bool = False
) -> list[str]:
    """
    Removes support files from a list of project-relative string paths,
    unless `include_support` is True.
    """
    if include_support or not language_server:
        return paths
    return [p for p in paths if not language_server.is_support_file(p)]
```

**5. Update All User-Facing File Enumeration Logic**
*   **Files:** Any tool or method that presents a list of files or symbols to the user.
*   **Action:** Use the new centralized helper from Step 4 to filter support files from the final results. This is critical for methods like:
    *   `project.py` -> `gather_source_files()`
    *   `ls.py` -> `request_full_symbol_tree()`, `request_analyzed_files()`, and `request_references()`
    *   Tools like `get_symbols_overview` and `find_symbol`.

**6. Update the Svelte Adapter to Declare its Support Files**
*   **File:** `src/solidlsp/language_servers/svelte_language_server.py`
*   **Action:** In the `SvelteProxyLanguageServer` class, override the `get_support_files` method.

```python
# In SvelteProxyLanguageServer class

@override
def get_support_files(self) -> list[str]:
    """Declares SvelteKit's generated type files as essential support files."""
    return [
        ".svelte-kit/**/*.d.ts",
        ".svelte-kit/ambient.d.ts",
        ".svelte-kit/tsconfig.json",
    ]
```

**Final Verification:**

The implementation will be successful when all of the following criteria are met:
*   **Read Access:** `read_file` can successfully read `.svelte-kit/types/src/routes/$types.d.ts`.
*   **Write Protection:** `replace_regex` targeting `.svelte-kit/types/src/routes/$types.d.ts` **raises a `ValueError`** with the "read-only" message.
*   **Result Filtering:** `get_symbols_overview` and `find_symbol` do **not** show any symbols from files within the `.svelte-kit` directory.
*   **Unit Test for Scoped Override:** A unit test must confirm that `validate_relative_path('node_modules/some-file', ls, write=True)` **still fails with the original "ignored" error**, proving the override is correctly scoped only to declared support files.