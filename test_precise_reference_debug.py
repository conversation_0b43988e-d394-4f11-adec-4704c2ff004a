#!/usr/bin/env python3
"""
Precise debug script to test reference finding with correct positions.
"""

import os
import sys
import logging
from pathlib import Path

# Add the src directory to the Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from solidlsp.language_servers.typescript_language_server import TypeScriptLanguageServer
from solidlsp.ls_config import LanguageServerConfig
from solidlsp.ls_logger import LanguageServerLogger

def test_precise_references():
    """Test reference finding with precise positions."""
    
    # Setup
    test_repo_path = "/Users/<USER>/MCP/serena/test/resources/repos/svelte/test_repo"
    
    # Create logger
    logger = LanguageServerLogger(log_level=logging.DEBUG)
    
    # Create config
    from solidlsp.ls_config import Language
    config = LanguageServerConfig(code_language=Language.TYPESCRIPT)
    
    print(f"Testing precise reference finding in: {test_repo_path}")
    
    # Create TypeScript language server
    ts_server = TypeScriptLanguageServer(config, logger, test_repo_path)
    
    try:
        with ts_server.start_server():
            print("TypeScript server started successfully")
            
            # Test formatName with different positions
            print("\n=== Testing formatName at different positions ===")
            
            # Position 1: Start of function declaration (line 3, col 0) - 0-indexed: (2, 0)
            print("\nTesting at function start (2, 0):")
            refs = ts_server.request_references("lib/utils.ts", 2, 0)
            print(f"Found {len(refs)} references")
            
            # Position 2: Function name (line 3, col 16) - 0-indexed: (2, 16)
            print("\nTesting at function name (2, 16):")
            refs = ts_server.request_references("lib/utils.ts", 2, 16)
            print(f"Found {len(refs)} references")
            for ref in refs:
                print(f"  Reference: {ref['relativePath']}:{ref['range']['start']['line']+1}:{ref['range']['start']['character']+1}")
            
            # Position 3: Inside function name (line 3, col 20) - 0-indexed: (2, 20)
            print("\nTesting inside function name (2, 20):")
            refs = ts_server.request_references("lib/utils.ts", 2, 20)
            print(f"Found {len(refs)} references")
            for ref in refs:
                print(f"  Reference: {ref['relativePath']}:{ref['range']['start']['line']+1}:{ref['range']['start']['character']+1}")
            
            # Test User interface for comparison
            print("\n=== Testing User interface for comparison ===")
            
            # Position: Interface name (line 2, col 17) - 0-indexed: (1, 17)
            print("\nTesting User interface at (1, 17):")
            refs = ts_server.request_references("lib/store.ts", 1, 17)
            print(f"Found {len(refs)} references")
            for ref in refs:
                print(f"  Reference: {ref['relativePath']}:{ref['range']['start']['line']+1}:{ref['range']['start']['character']+1}")
                
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_precise_references()