#!/usr/bin/env python3
"""
Simple test to verify the find_referencing_symbols fix works.
"""

import sys
import os
import logging
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from solidlsp.language_servers.svelte_language_server import SvelteProxyLanguageServer
from solidlsp.ls_config import LanguageServerConfig, Language
from solidlsp.ls_logger import LanguageServerLogger
from serena.symbol import LanguageServerSymbolRetriever

def test_fix():
    """Test that find_referencing_symbols works for TypeScript functions."""
    
    repo_path = "/Users/<USER>/MCP/serena/test/resources/repos/svelte/test_repo"
    config = LanguageServerConfig(Language.SVELTE)
    logger = LanguageServerLogger(logging.getLogger("test"), logging.INFO)
    ls = SvelteProxyLanguageServer(config, logger, repo_path)
    
    try:
        ls.start()
        print("✓ Language server started")
        
        retriever = LanguageServerSymbolRetriever(ls)
        
        # Test the fix: find_referencing_symbols should work without errors
        refs = retriever.find_referencing_symbols("formatName", "lib/utils.ts")
        print(f"✓ find_referencing_symbols returned {len(refs)} references")
        
        if len(refs) > 0:
            print("✓ References found:")
            for ref in refs:
                print(f"  - {ref.symbol.name} in {ref.symbol.location.relative_path}")
        else:
            print("! No references found (this might be expected depending on test data)")
            
        print("\n✓ Fix verification successful - no errors occurred!")
        return True
        
    except Exception as e:
        print(f"✗ Fix verification failed: {e}")
        return False
    finally:
        ls.stop()
        print("✓ Language server stopped")

if __name__ == "__main__":
    success = test_fix()
    sys.exit(0 if success else 1)