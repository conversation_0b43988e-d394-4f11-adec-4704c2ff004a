# Serena MCP SvelteKit Adapter Testing Guide

## Available MCP Tools

Based on the Serena MCP server logs, here are all available tools for testing the SvelteKit adapter:

### 🔍 **Symbol Discovery & Navigation**
- `mcp__serena__get_symbols_overview` - Get overview of symbols in files/directories
- `mcp__serena__find_symbol` - Find specific symbols by name pattern
- `mcp__serena__find_referencing_symbols` - Find symbols that reference a given symbol

### 📁 **File Operations**
- `mcp__serena__list_dir` - List directory contents
- `mcp__serena__find_file` - Find files by name pattern
- `mcp__serena__search_for_pattern` - Search for text patterns in files

### ✏️ **Code Editing**
- `mcp__serena__replace_regex` - Replace text using regex patterns
- `mcp__serena__replace_symbol_body` - Replace entire symbol body
- `mcp__serena__insert_after_symbol` - Insert code after a symbol
- `mcp__serena__insert_before_symbol` - Insert code before a symbol

### 💾 **Memory Management**
- `mcp__serena__write_memory` - Store information for future use
- `mcp__serena__read_memory` - Retrieve stored information
- `mcp__serena__list_memories` - List all stored memories
- `mcp__serena__delete_memory` - Delete stored information

### ⚙️ **Configuration & Workflow**
- `mcp__serena__get_current_config` - Get current configuration
- `mcp__serena__switch_modes` - Switch operational modes
- `mcp__serena__remove_project` - Remove project from configuration
- `mcp__serena__restart_language_server` - Restart language server
- `mcp__serena__initial_instructions` - Get initial setup instructions
- `mcp__serena__check_onboarding_performed` - Check if onboarding completed
- `mcp__serena__onboarding` - Perform project onboarding

### 🧠 **Meta-Operations**
- `mcp__serena__think_about_collected_information` - Analyze gathered info
- `mcp__serena__think_about_task_adherence` - Check task progress
- `mcp__serena__think_about_whether_you_are_done` - Evaluate completion
- `mcp__serena__summarize_changes` - Summarize modifications made
- `mcp__serena__prepare_for_new_conversation` - Reset for new session

---

## 🧪 **SvelteKit Adapter Testing Protocol**

Use this comprehensive prompt to test the Svelte adapter on a real SvelteKit project:

### **Testing Prompt for AI Assistant**

```
I need you to perform a comprehensive validation of the Serena MCP SvelteKit adapter. You have access to Serena MCP tools for a SvelteKit project with TypeScript and Svelte 5 runes.

**Testing Protocol:**

1. **Project Discovery**
   - Use `mcp__serena__get_current_config` to confirm project status
   - Use `mcp__serena__get_symbols_overview` with `relative_path='src'` to discover project structure
   - Use `mcp__serena__list_dir` to explore key directories like `src/lib`, `src/routes`

2. **Symbol Navigation Testing**
   - Use `mcp__serena__find_symbol` to locate TypeScript interfaces/types in `.ts` files
   - Use `mcp__serena__find_symbol` to locate Svelte components and their props/state
   - Test finding symbols with Svelte 5 runes like `$props()`, `$state()`, `$effect()`

3. **Cross-Language Intelligence Validation**
   - Find a TypeScript symbol (function, interface, or exported variable) from a `.ts` file
   - Use `mcp__serena__find_referencing_symbols` to find all references to that symbol
   - Verify it finds references in both `.ts` files AND `.svelte` files that import it
   - Test the reverse: find a symbol used in a `.svelte` file and trace it back to its `.ts` definition

4. **SvelteKit-Specific Testing**
   - Look for SvelteKit route files (`+page.svelte`, `+layout.svelte`, `+page.server.ts`)
   - Test symbol resolution for SvelteKit's generated types (like `PageProps`, `LayoutProps`)
   - Check imports from `$lib` aliases and `$types` imports

5. **Real-World Scenario Testing**
   - Pick a commonly used symbol (like an auth object, API client, or utility function)
   - Use `mcp__serena__find_referencing_symbols` to find all its usages
   - Verify it correctly identifies references across the entire project
   - Document any files where expected references are missing

6. **Error Handling & Edge Cases**
   - Test with files that have Svelte 5 runes that might cause transformation errors
   - Test with complex TypeScript types and generics
   - Test with deeply nested imports and re-exports

**Expected Results:**
- All tools should respond without errors
- Cross-language symbol resolution should work bidirectionally (TS ↔ Svelte)
- Reference finding should return actual file locations with line/column numbers
- Symbol overview should show both Svelte components and TypeScript symbols

**Report Format:**
For each test, report:
- ✅ PASS: Tool worked as expected, found X symbols/references
- ⚠️ PARTIAL: Tool worked but with limitations (describe)
- ❌ FAIL: Tool failed or returned empty/incorrect results (provide details)

**Focus Areas:**
- Cross-file symbol resolution between `.svelte` and `.ts` files
- SvelteKit route structure navigation
- Svelte 5 runes compatibility
- Import alias resolution (`$lib/`, `$types`)
- Large project performance

Document any failures with specific file paths, symbol names, and expected vs. actual results.
```

---

## 🎯 **Key Test Cases for SvelteKit Projects**

### **Test Case 1: Auth Integration**
```
1. Find the main auth object/function in src/lib/server/auth.ts
2. Use find_referencing_symbols to locate all its usages
3. Should find references in:
   - Server-side route files (+page.server.ts, +layout.server.ts)
   - Hook files (hooks.server.ts)
   - Type definition files (app.d.ts)
```

### **Test Case 2: Component Props**
```
1. Find a Svelte component with TypeScript props
2. Locate the props interface definition
3. Find all references to that interface
4. Should trace between .svelte files and .ts type files
```

### **Test Case 3: Utility Functions**
```
1. Find utility functions in src/lib/utils.ts
2. Use find_referencing_symbols to find usages
3. Should find references in both .svelte components and .ts files
```

### **Test Case 4: SvelteKit Types**
```
1. Look for generated types like PageProps, LayoutProps
2. Find their usage in +page.svelte and +layout.svelte files
3. Test symbol resolution for $types imports
```

---

## 🚨 **Known Issues & Workarounds**

### **Missing Tools**
- There is NO `request_definition` tool (this was a hallucination)
- Use `find_symbol` to locate symbol definitions instead

### **Expected Warnings**
- Svelte 5 runes may cause transformation warnings (this is normal)
- Import statements may show debug messages (not errors)

### **Troubleshooting**
- If getting empty results, try `restart_language_server`
- Check logs for transformation errors or LSP communication issues
- Use `get_current_config` to verify project is properly activated

---

## 📊 **Success Criteria**

The SvelteKit adapter is working correctly if:

1. **Symbol Discovery**: Can find symbols in both `.svelte` and `.ts` files
2. **Cross-Language References**: `find_referencing_symbols` returns results for symbols used across file types
3. **SvelteKit Integration**: Recognizes route files, generated types, and $lib imports
4. **Svelte 5 Compatibility**: Handles modern runes syntax without critical errors
5. **Performance**: Responds within reasonable time for project-wide queries

Test on a real SvelteKit project with multiple routes, shared utilities, and TypeScript integration for the most comprehensive validation.